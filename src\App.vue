<template>
  <div id="app">
    <router-view />
  </div>
</template>
<script>
export default {
  mounted() {
    // 移除ZWJSBridge依赖，直接设置页面标题
    document.title = "医保个人授权";

    // 设置默认的UI样式属性（原ZWJSBridge.getUiStyle功能）
    this.setDefaultUIStyle();
  },
  methods: {
    setDefaultUIStyle() {
      // 设置默认的字体大小属性（使用normal模式的配置）
      window.document.documentElement.setAttribute("data-1218", "12");
      window.document.documentElement.setAttribute("data-1318", "13");
      window.document.documentElement.setAttribute("data-1420", "14");
      window.document.documentElement.setAttribute("data-1618", "16");
      window.document.documentElement.setAttribute("data-1622", "16");
      window.document.documentElement.setAttribute("data-1824", "18");
      window.document.documentElement.setAttribute("data-1924", "19");
      window.document.documentElement.setAttribute("data-2030", "20");
      window.document.documentElement.setAttribute("data-4060", "40");
      window.document.documentElement.setAttribute("data-168-auto", "168");
      window.document.documentElement.setAttribute("data-auto-100", "auto");
    }
  }
};
</script>
<style lang="scss">
@import "@/assets/css/public.scss";
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.Diy {
  border-radius: 0.08rem !important;
  .van-dialog__header {
    @include font_size_16_22($fontSize-16);
  }
  .van-dialog__message {
    @include font_size_14_20($fontSize-14);
  }
  .van-button {
    @include font_size_16_22($fontSize-16);
  }
}
</style>
