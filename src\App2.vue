<template>
  <div id="app2"></div>
</template>

<script>
import {
  randomString,
  deepCopy,
  findObjectArray,
  encrypt,
  noPassByName,
} from "@/util/index";
export default {
  created() {
    console.log(encrypt("郭元扬"));
    console.log(encrypt("330724199502054815"));

    // ZWJSBridge.onReady(() => {
    //   ZWJSBridge.openLink({
    //     url: "https://puser.zjzwfw.gov.cn/sso/mobile.do?action=oauth&scope=1&servicecode=BCDSGA_8886a94ce5a4569b7d5ef5f38e7bc9cb&redirectUrl=https://mapi.zjzwfw.gov.cn/web/mgop/gov-open/zj/2002243178/reserved/index.html",
    //   })
    //     .then(res => {
    //       console.log(res);
    //     })
    //     .catch(err => {
    //       console.log(err);
    //     });
    // });
  },
};
</script>

<style lang="scss">
body {
  margin: 0;
  box-sizing: border-box;
}
</style>
