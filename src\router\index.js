import Vue from "vue";
import VueRouter from "vue-router";
import Home from "../views/Home.vue";
import store from "@/store";
import { getUserInfo } from "@/api/index";
import { randomString } from "@/util/index";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "Home",
    redirect: "/Home",
    // component: Home,
  },
  {
    path: "/transfer",
    name: "transfer",
    component: () => import("@/views/transfer.vue"),
  },
  {
    path: "/Home",
    name: "Home",
    component: () => import("@/views/Home.vue"),
  },
  // 人脸
  {
    path: "/face",
    name: "accredit",
    component: () => import("@/views/face.vue"),
  },
  // 授权
  {
    path: "/accredit",
    name: "accredit",
    component: () => import("@/views/accredit.vue"),
  },
  // 授权协议
  {
    path: "/agreement",
    name: "agreement",
    component: () => import("@/views/agreement.vue"),
  },
  // 服务
  {
    path: "/serve",
    name: "serve",
    component: () => import("@/components/inquire/serve.vue"),
  },
  // 服务明细
  {
    path: "/serveDet",
    name: "serveDet",
    component: () => import("../components/inquire/serveDet.vue"),
  },
  // 场景
  {
    path: "/channel",
    name: "channel",
    component: () => import("@/components/channel/health.vue"),
  },
  // 场景明细
  {
    path: "/channelDet",
    name: "channelDet",
    component: () => import("../components/channel/healthDet.vue"),
  },
  // 服务授权管理
  {
    path: "/authManage",
    name: "authManage",
    component: () => import("@/views/authManage.vue"),
  },
  // 服务授权管理
  {
    path: "/authManageScene",
    name: "authManageScene",
    component: () => import("@/views/authManageScene.vue"),
  },
  // 授权成功
  {
    path: "/authSuccess",
    name: "authSuccess",
    component: () => import("@/views/authSuccess.vue"),
  },
  //未找到
  {
    path: "/notFind",
    name: "notFind",
    component: () => import("../views/notFind.vue"),
  },
  {
    path: "/test",
    name: "test",
    component: () => import("../views/test.vue"),
  },
];

const router = new VueRouter({
  routes,
});
// var ua = window.navigator.userAgent.toLowerCase();
// var container = "";
// if (
//   ua.includes("miniprogram/wx") ||
//   window.__wxjs_environment === "miniprogram"
// ) {
//   container = "wx";
// } else if (ua.match(/AlipayClient/i) == "alipayclient") {
//   container = "ali";
// } else {
//   container = "h5";
// }
router.beforeEach((to, from, next) => {
  // 移除ZWJSBridge.close调用，直接进行路由跳转
  console.log("路由跳转:", from.name, "->", to.name);
  next();
});

export default router;
