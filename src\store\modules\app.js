export default {
  namespaced: true,
  state: {
    errorData: [],
    fontSize: 1,
    userInfo: JSON.parse(window.sessionStorage.getItem("userInfo")) || {},
    business: [],
  },
  getters: {},
  mutations: {
    setErrorData(state, data) {
      state.errorData = data;
    },
    setFontSize(state, data) {
      state.fontSize = data;
    },
    setUserInfo(state, data) {
      state.userInfo = data;
      window.sessionStorage.setItem("userInfo", JSON.stringify(data));
    },
    setBusiness(state, data) {
      state.business = data;
    },
  },
  actions: {
    collectError({ state }, info) {
      console.log(state.errorData, info, "errorDataerrorData");
      state.errorData.push(info);
    },
  },
};
