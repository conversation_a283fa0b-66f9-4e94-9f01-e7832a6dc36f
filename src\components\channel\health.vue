!
<template>
  <div class="main">
    <!-- header -->
    <div class="header">
      <div class="header-content">
        <div class="header-left">
          <img src="../../assets/image/touxiang.png" alt />
        </div>
        <div class="header-right">
          <div>
            <p class="right-one">
              {{ noPassByName(this.$store.state.app.userInfo.username) }}
            </p>
            <p class="right-two">
              <span>
                <img src="../../assets/image/shenfenzheng.png" alt />
              </span>
              <span>{{ IDnumber }}</span>
            </p>
          </div>
          <div @click="authManage">
            <p class="auth-manage">授权管理</p>
          </div>
        </div>
      </div>
    </div>
    <!-- sec -->
    <div class="sec">
      <div class="service-type">
        <img
          v-if="serviceType === 1"
          style="position: absolute; width: 50%; left: 0; top: 0; z-index: 1"
          src="../../assets/image/left.png"
          alt=""
        />
        <img
          v-else
          style="position: absolute; width: 50%; right: 0; top: 0; z-index: 1"
          src="../../assets/image/right.png"
          alt=""
        />
        <div style="position: absolute; left: 0; top: 0; z-index: 2">
          <p
            @click="changeServiceType(1)"
            :style="serviceType === 1 ? { color: '#428FFC' } : {}"
          >
            个人服务
          </p>
          <img
            v-if="serviceType === 1"
            src="../../assets/image/select.png"
            alt=""
          />
        </div>
        <div style="position: absolute; right: 0; top: 0; z-index: 2">
          <p
            @click="changeServiceType(2)"
            :style="serviceType === 2 ? { color: '#428FFC' } : {}"
          >
            对公服务
          </p>
          <img
            v-if="serviceType === 2"
            src="../../assets/image/select.png"
            alt=""
          />
        </div>
      </div>
      <div
        style="padding: 0 0.15rem"
        v-if="serviceType === 1 || (serviceType === 2 && business)"
      >
        <div class="change-business" v-if="businessName">
          <p>{{ businessName }}</p>
          <p @click="showBusiness = true">切换</p>
        </div>
        <div class="sec-one">
          <div class="two-one">
            <div>
              <van-cell @click="selsectD1">
                <input
                  placeholder="开始时间"
                  :value="beginTime"
                  type="text"
                  readonly="readonly"
                />
              </van-cell>
            </div>
            <div>至</div>
            <div>
              <van-cell @click="selsectD2">
                <input
                  placeholder="结束时间"
                  :value="overTime"
                  type="text"
                  readonly="readonly"
                />
              </van-cell>
            </div>
          </div>
        </div>
        <div class="sec-two">
          <van-button type="info" disabled v-if="!overTime || !beginTime"
            >查询</van-button
          >
          <van-button type="info" @click="btninRuire" v-else>查询</van-button>
        </div>
        <div class="sec-three">查询结果最多显示近一年</div>
      </div>
      <div class="select-business" v-else @click="handleSelectBusiness">
        <img src="@/assets/image/add.png" alt="" />
        <p>选择企业</p>
      </div>
    </div>
    <!-- 选择时间 -->
    <van-popup v-model="showTime" position="bottom" :style="{ height: '50%' }">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        confirm-button-text="确定"
        :columns-order="['year', 'month', 'day']"
        :formatter="formatter"
        @confirm="confirmData"
        @cancel="cancelData"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </van-popup>
    <van-popup v-model="showTime1" position="bottom" :style="{ height: '50%' }">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        confirm-button-text="确定"
        :columns-order="['year', 'month', 'day']"
        :formatter="formatter"
        @confirm="confirmData1"
        @cancel="cancelData1"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </van-popup>
    <!-- sec-bot -->
    <div v-if="showExamine">
      <div class="sec-bot" v-if="sceneList.length">
        <div
          class="three-one"
          @click="examineDet(item)"
          v-for="(item, index) in sceneList"
          :key="index"
        >
          <div class="one-one">
            <div>
              <p>{{ item.sceneName }}</p>
            </div>
            <div class="examine">
              查看明细
              <span></span>
            </div>
          </div>
        </div>
      </div>
      <div v-else style="margin-top: 1.5rem">
        <empty />
      </div>
      <div class="footer-info">
        <footer-info></footer-info>
      </div>
    </div>
    <div v-else class="footer-info">
      <footer-info></footer-info>
    </div>

    <!-- 提示框 -->
    <!-- <div class="footer-title">本服务由杭州市医保局提供</div> -->
    <!-- footer -->
    <div class="footer">
      <div>
        <div @click="serveCondition"><p>查看服务调用情况</p></div>
        <div @click="checkChannel"><p>查看场景调用情况</p></div>
      </div>
    </div>
    <van-popup
      v-model="showBusiness"
      position="bottom"
      :style="{ height: '50%' }"
    >
      <van-picker
        title=""
        show-toolbar
        :columns="businessList"
        @confirm="onConfirm"
        @cancel="showBusiness = false"
      />
    </van-popup>
  </div>
</template>
<script>
import common, { randomString, noPassByName, encrypt } from "@/util/index";
import { authoChannels } from "@/api/index";
import empty from "../empty.vue";
import { Toast } from "vant";
export default {
  components: {
    empty,
  },
  data() {
    return {
      businessList: this.$store.state.app.business,
      showBusiness: false,
      serviceType: 1, // 1：个人2：对公
      business: "",
      businessName: "",
      minDate: new Date("2022/01/01"),
      maxDate: new Date(),
      currentDate: new Date(),
      codeId: "", //获取对应id值
      codeName: "", //channelName
      Code: "", //serviceCode
      sceneList: [], //医保服务列表
      //开始/结束时间
      beginTime: "",
      overTime: "",
      showTime: false,
      showTime1: false,
      showExamine: false, //是否显示查看明细
      IDnumber: this.$store.state.app.userInfo.idnum.replace(
        /^(.{1})(?:\w+)(.{1})$/,
        "$1****************$2"
      ),
    };
  },
  methods: {
    noPassByName,
    handleSelectBusiness() {
    if (this.businessList.length === 0) {
      Toast("暂无数据"); // 弹出提示框
    } else {
      this.showBusiness = true; // 显示选择器
    }
  },
    onConfirm(val) {
      this.showBusiness = false;
      this.business = val.value;
      this.businessName = val.text;
    },
    changeServiceType(type) {
      this.serviceType = type;
    },
    // 授权管理
    authManage() {
      this.$router.push("/authManage");
    },
    // 查看服务
    serveCondition() {
      this.$router.push({
        path: "/serve",
      });
    },
    // 个人场景服务调用记录
    getAuthoCall() {
      let params = {
        status: "0",
        startTime: `${this.beginTime} 00:00:00`,
        endTime: `${this.overTime} 23:59:59`,
        logTraceID: randomString(),
      };
      params.certNo =
        this.serviceType === 1
          ? encrypt(this.$store.state.app.userInfo.idnum)
          : encrypt(this.business);
      authoChannels(params).then(res => {
        this.showExamine = true;
        if (res.records) this.sceneList = res.records;
      });
    },
    // 查看场景
    checkChannel() {
      this.$router.push({
        path: "/channel",
      });
    },
    //点击查看明细
    examineDet(info) {
      this.$router.push({
        path: "/channelDet",
        query: {
          info: encodeURIComponent(JSON.stringify(info)),
          beginTime: this.beginTime,
          endTime: this.overTime,
        },
      });
    },
    btninRuire() {
      this.$nextTick(() => {
        this.getAuthoCall();
      });
    },
    //选择时间的取消确定
    confirmData(e) {
      this.beginTime = this.dayjs(e).format("YYYY-MM-DD");
      this.showTime = false;
      if (
        this.overTime &&
        new Date(this.overTime).getTime() < new Date(this.beginTime).getTime()
      ) {
        this.beginTime = "";
        Toast("开始时间不能大于结束时间");
      } else if (
        new Date(this.overTime).getTime() - new Date(this.beginTime).getTime() >
        31536000000
      ) {
        this.beginTime = "";
        Toast("选择的时间范围超过了一年，请缩小范围后重试");
      }
    },
    cancelData() {
      this.showTime = false;
    },
    cancelData1() {
      this.showTime1 = false;
    },
    confirmData1(e) {
      this.overTime = this.dayjs(e).format("YYYY-MM-DD");
      this.showTime1 = false;
      if (
        this.beginTime &&
        new Date(this.beginTime).getTime() > new Date(this.overTime).getTime()
      ) {
        this.overTime = "";
        Toast("结束时间不能小于开始时间");
      } else if (
        new Date(this.overTime).getTime() - new Date(this.beginTime).getTime() >
        31536000000
      ) {
        this.overTime = "";
        Toast("选择的时间范围超过了一年，请缩小范围后重试");
      }
    },
    // 弹出日期
    formatter(type, val) {
      if (type === "year") {
        return val + "年";
      }
      if (type === "month") {
        return val + "月";
      }
      if (type === "day") {
        return val + "日";
      }
      return val;
    },
    selsectD1(e) {
      this.showTime = true;
    },
    selsectD2(e) {
      this.showTime1 = true;
    },
  },
  created() {
    this.createTime = new Date().getTime();
  },
  mounted() {
    this.mountedTime = new Date().getTime();
    common.changeTitle("医保个人授权");
    this.$buryPoint(this.mountedTime - this.createTime);
  },
};
</script>
<style lang="scss" scoped>
@import "@/assets/css/public.scss";
.main {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f5;
  ::v-deep.van-picker__toolbar {
    @include font_size_16_22($fontSize-16);
    border-bottom: 1px solid #ececec;
    .van-picker__cancel {
      @include font_size_16_22($fontSize-16);
      color: #999999 !important;
    }
    .van-picker__confirm {
      @include font_size_16_22($fontSize-16);
      color: #3a5ce0 !important;
    }
  }
  ::v-deep.van-picker-column__item {
    color: #000;
    @include font_size_20_30($fontSize-20);
    font-weight: 550;
  }
  .header {
    width: 100%;
    height: 1.68rem;
    background: url("../../assets/image/beijing1.png") no-repeat center;
    background-size: cover;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    .header-content {
      width: 100%;
      height: 0.84rem;
      display: flex;
      padding: 0.05rem 0.16rem;
    }
    .header-left {
      width: 0.47rem;
      height: 0.47rem;
      box-sizing: border-box;
      img {
        width: 100%;
        height: 100%;
        margin-top: -0.5rem;
        vertical-align: middle;
      }
    }
    .header-right {
      display: flex;
      flex: 1;
      justify-content: space-between;
      height: 1rem;
      margin-top: 0.1rem;
      margin-left: 0.1rem;
      vertical-align: middle;
      .right-one {
        @include font_size_18_24($fontSize-18);
        height: 0.18rem;
        color: #ffffff;
        margin-bottom: 0.08rem;
      }
      .right-two {
        color: #ffffff;
        @include font_size_13_18($fontSize-13);
        img {
          width: 0.15rem;
          height: 0.12rem;
          margin-right: 0.05rem;
          vertical-align: middle;
        }
      }
      .auth-manage {
        min-width: 0.72rem;
        padding: 0 0.09rem;
        width: auto;
        text-align: center;
        line-height: 0.28rem;
        border: 1px solid #ffffff;
        border-radius: 0.14rem;
        @include font_size_14_20($fontSize-14);
        color: white;
      }
    }
  }
  .sec {
    margin: -1rem auto;
    width: 3.45rem;
    min-height: 2.05rem;
    box-sizing: border-box;
    border-radius: 0.1rem;
    background: #ffffff;
    .service-type {
      position: relative;
      display: flex;
      height: 0.6rem;
      background-image: url("../../assets/image/select-bg.png");
      background-size: 100% 100%;
      & > div {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        p {
          text-align: center;
          margin-bottom: 0.06rem;
          @include font_size_16_18($fontSize-16);
        }
        img {
          width: 0.16rem;
          height: 0.03rem;
        }
      }
    }
    .select-business {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin-top: 0.25rem;
      img {
        width: 0.42rem;
        height: 0.45rem;
      }
      p {
        margin-top: 0.06rem;
        @include font_size_14_20($fontSize-14);
      }
    }
    .change-business {
      display: flex;
      align-items: center;
      margin-bottom: 0.1rem;
      p {
        &:nth-of-type(1) {
          font-weight: bolder;
          @include font_size_14_20($fontSize-14);
          color: #363a44;
          margin: 0 0.1rem 0 0.17rem;
        }
        &:nth-of-type(2) {
          width: 0.25rem;
          height: 0.15rem;
          border-radius: 2px;
          border: 1px solid #428ffc;
          font-size: 0.1rem;
          color: #428ffc;
          text-align: center;
          line-height: 0.15rem;
        }
      }
    }
    .sec-one {
      height: 0.57rem;
      .two-one {
        width: 100%;
        box-sizing: border-box;
        display: flex;
        justify-content: space-around;
        :nth-child(2) {
          @include font_size_16_22($fontSize-16);
          color: #999999;
          line-height: 0.48rem;
          vertical-align: middle;
        }
        input::-webkit-input-placeholder {
          color: #999999;
          font-weight: 500;
        }
        input {
          @include font_size_16_18($fontSize-16);
          font-weight: 550;
          padding-bottom: 8px;
          text-align: left;
          color: #333333;
          border: none;
          border-bottom: 1px solid #cdcdcd !important;
          width: 100%;
        }
      }
      .two-two {
        width: 100%;
        padding: 0 6%;
        .van-button {
          width: 90%;
          font-size: 35px;
          border-radius: 35px;
          height: 90px;
          margin: 25px auto;
        }
      }
      .two-three {
        color: #2a73fd;
        font-size: 30px;
        text-align: center;
      }
    }
    .sec-two {
      height: 0.47rem;
      box-sizing: border-box;
      position: relative;
      margin: 5px;
      .van-button {
        @include font_size_16_22($fontSize-16);
        border-radius: 0.2rem;
        position: absolute;
        width: 100%;
      }
    }
    .sec-three {
      font-weight: 550;
      letter-spacing: 0.011rem;
      text-align: center;
      @include font_size_12_18($fontSize-12);
      padding: 0.07rem 0 0.15rem 0;
      color: #2a73fd;
    }
  }
  .sec-bot::-webkit-scrollbar {
    width: 0;
  }
  .sec-bot {
    margin: 0 auto;
    margin-top: 116px;
    height: calc(100vh - 4rem);
    width: 3.45rem;
    overflow-y: scroll;
    border-radius: 0.1rem;
    background: #ffffff;
    .three-one {
      padding: 0.139rem;
      .one-one {
        height: 0.44rem;
        padding: 0 0.16rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 0.01rem solid #ececec;
        div {
          @include font_size_14_20($fontSize-14);
          :first-child {
            color: #363a44;
          }
          :nth-child(2) {
            color: #999999;
            @include font_size_14_20($fontSize-14);
          }
        }
      }
    }
    .examine {
      box-sizing: border-box;
      line-height: 0.3rem;
      vertical-align: middle;
      color: #999999;
      vertical-align: middle;
      span {
        display: inline-block;
        font-size: 0.3em;
        padding: 0.4em;
        width: 0.03rem;
        height: 0.03rem;
        vertical-align: middle !important;
        border-top: 0.015rem solid #999999;
        border-right: 0.015rem solid #999999;
        transform: rotate(45deg);
      }
    }
  }
  .footer-title {
    padding: 0.15rem;
    font-size: 0.12rem;
    text-align: center;
    width: 100%;
    letter-spacing: 0.02rem;
    height: 0.115rem;
    color: #999999;
    position: fixed;
    bottom: 0.52rem;
    font-family: AlibabaPuHuiTiR;
  }
  .footer {
    width: 100%;
    height: 0.52rem;
    position: fixed;
    box-shadow: 0px 0px 0.04rem 0px #c7c2c2;
    bottom: 0;
    // padding: 10px;
    background: #ffffff;
    display: flex;
    align-items: center;
    div {
      flex: 1;
      font-weight: 550;
      @include font_size_16_22($fontSize-16);
      letter-spacing: 4 !important;
      display: flex;
      justify-content: space-around;
      div {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      :first-child {
        letter-spacing: 4 !important;
        p {
          color: #999999;
          @include width_auto_100($width-auto);
          font-weight: 500;
        }
      }
      :nth-child(2) {
        letter-spacing: 4 !important;
        p {
          color: #2a73fd;
          @include width_auto_100($width-auto);
          font-weight: 500;
        }
      }
    }
  }
  .footer-info {
    position: fixed;
    bottom: 0.52rem;
    width: 100%;
  }
}
</style>
