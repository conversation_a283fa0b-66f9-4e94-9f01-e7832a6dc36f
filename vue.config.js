const path = require("path");
const pathResolve = dir => {
  return path.join(__dirname, dir);
};
const pages = {
  page1: {
    entry: "src/main.js",
    template: "public/index.html",
    filename: "index.html",
  },
  page2: {
    entry: "src/main2.js",
    template: "public/index2.html",
    filename: "index2.html",
  },
};
module.exports = {
  pages,
  publicPath: './',
  outputDir: "dist",
  devServer: {
    host: "0.0.0.0",
    port: 8989,
    proxy: {
      '/mamp': {
        target: 'https://zhybyf.ybj.zj.gov.cn/',
        changeOrigin: true,
        secure: false,
        headers: {
          'Referer': 'https://zhyb.ybj.zj.gov.cn',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
        logLevel: 'debug', // 开启调试日志
        // pathRewrite: { '^/mamp': '/mamp' },
      },
      '/csaf': {
        target: 'https://zhyb.ybj.zj.gov.cn/',
        changeOrigin: true,
        secure: false,
        // pathRewrite: { '^/csaf': '/csaf' },
      }
    },
  },
  configureWebpack: {
    resolve: {
      alias: {
        "@": pathResolve("src"),
        "@img": pathResolve("src/assets/image"),
      },
    },
  },
};
