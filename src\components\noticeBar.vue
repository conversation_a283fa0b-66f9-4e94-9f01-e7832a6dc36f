<!-- 公告栏组件 -->
<template>
  <div class="notice-bar" @click="tipClick" ref="wrap">
    <img
      style="
        margin-left: 0.08rem;
        margin-right: 0.02rem;
        width: 0.11rem;
        height: 0.11rem;
      "
      src="../assets/image/laba.png"
      alt=""
    />
    <div class="notice-bar__icon">
      <span>{{ sub_title }}</span>
    </div>
    <div class="notice-bar__wrap">
      <div ref="content" class="notice-bar__content" :style="contentStyle">
        <p>{{ text }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NoticeBar",
  props: {
    sub_title: {
      type: String,
      default: "",
    },
    text: {
      type: String,
      default: "",
    },
    speed: {
      type: Number,
      default: 20,
    },
    defaultWidth: {
      type: Number,
      default: 375,
    },
  },
  data() {
    return {
      contentStyle: {
        transitionDuration: "0s",
        transform: "translateX(0px)",
      },
      wrapWidth: 0,
      contentWidth: 0,
      time: 0,
      timer: null,
      convertSpeed: 1,
    };
  },
  created() {},
  mounted() {
    setTimeout(() => {
      this.init();
    }, 1000);
  },
  watch: {},
  methods: {
    init() {
      this.$nextTick(() => {
        const _width = window.innerWidth;
        this.convertSpeed = (_width / this.defaultWidth) * this.speed; // 根据分辨率转化成不同的速度
        this.wrapWidth = this.$refs.wrap.offsetWidth;
        this.contentWidth = this.$refs.content.offsetWidth;
        if (this.wrapWidth < this.contentWidth) {
          this.startAnimate();
          this.timer = setInterval(() => {
            this.startAnimate();
          }, this.time * 1000 + 1500);
          this.$once("hook:beforeDestroy", () => {
            clearInterval(this.timer);
            this.timer = null;
          });
        }
      });
    },
    startAnimate() {
      this.contentStyle.transitionDuration = "0s";
      this.contentStyle.transform = "translateX(0px)";
      this.time = (this.contentWidth - this.wrapWidth) / this.convertSpeed;
      setTimeout(() => {
        this.contentStyle.transitionDuration = this.time + "s";
        const move = this.contentWidth - this.wrapWidth;
        this.contentStyle.transform = `translateX(-${move}px)`;
      }, 1000);
    },
    tipClick() {
      this.$emit("click");
    },
  },
};
</script>
<style scoped lang="scss">
.notice-bar {
  padding-right: 0.09rem;
  position: relative;
  width: 100%;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #eee;
  display: flex;
  align-items: center;
  .notice-bar__icon {
    position: static;
    z-index: 2;
    display: flex;
    align-items: center;
    color: black;
    font-size: 0.14rem;
  }
  .notice-bar__wrap {
    position: relative;
    display: flex;
    flex: 1;
    height: 100%;
    align-items: center;
    overflow: hidden;
    .notice-bar__content {
      position: absolute;
      left: 0px;
      white-space: nowrap;
      transition-timing-function: linear;
      p {
        margin-left: -0.05rem;
        font-size: 0.12rem;
        transform: scale(0.92);
        font-family: PingFang SC;
        font-weight: 400;
        color: #428ffc;
      }
    }
  }
}
</style>
