import Vue from "vue";
import App from "./App2.vue";
// import store from "./store";
// import "../src/assets/css/reset.css";
// import "../src/assets/js/rem";
// import uniSDK from '../src/assets/js/wechatTest'//测试
// import '../src/assets/js/openIcon'//正式
// setRemInit();
// import Vant from "vant";
// import dayjs from "dayjs";
// //浙里办jsSDK
// ZWJSBridge.onReady(() => {
//   console.log("初始化完成后，执行bridge方法");
// });

// Vue.prototype.dayjs = dayjs;
// import "vant/lib/index.css";
// Vue.use(Vant);
Vue.config.productionTip = false;
new Vue({
  //   store,
  render: h => h(App),
}).$mount("#app2");
