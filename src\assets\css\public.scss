/*需要切换的字体变量*/
$fontSize-12: 0.12rem;
$fontSize-13: 0.13rem;
$fontSize-14: 0.14rem;
$fontSize-16: 0.16rem;
$fontSize-18: 0.18rem;
$fontSize-19: 0.19rem;
$fontSize-20: 0.2rem;
$fontSize-22: 0.22rem;
$fontSize-24: 0.24rem;
$fontSize-30: 0.3rem;
$fontSize-40: 0.4rem;
$fontSize-60: 0.6rem;
$width-100: 1rem;
$width-168: 1.68rem;
$width-auto: auto;

/*定义方法*/
@mixin font_size_12_18($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-1218="12"] & {
    font-size: $fontSize-12;
  }
  [data-1218="18"] & {
    font-size: $fontSize-18;
  }
}

@mixin font_size_13_18($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-1318="13"] & {
    font-size: $fontSize-13;
  }
  [data-1318="18"] & {
    font-size: $fontSize-18;
  }
}

@mixin font_size_14_20($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-1420="14"] & {
    font-size: $fontSize-14;
  }
  [data-1420="20"] & {
    font-size: $fontSize-20;
  }
}

@mixin font_size_16_18($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-1618="16"] & {
    font-size: $fontSize-16;
  }
  [data-1618="18"] & {
    font-size: $fontSize-18;
  }
}

@mixin font_size_16_22($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-1622="16"] & {
    font-size: $fontSize-16;
  }
  [data-1622="22"] & {
    font-size: $fontSize-22;
  }
}

@mixin font_size_18_24($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-1824="18"] & {
    font-size: $fontSize-18;
  }
  [data-1824="24"] & {
    font-size: $fontSize-24;
  }
}

@mixin font_size_19_24($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-1924="19"] & {
    font-size: $fontSize-19;
  }
  [data-1924="24"] & {
    font-size: $fontSize-24;
  }
}

@mixin font_size_20_30($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-2030="20"] & {
    font-size: $fontSize-20;
  }
  [data-2030="30"] & {
    font-size: $fontSize-30;
  }
}

@mixin font_size_40_60($fontSize) {
  font-size: $fontSize;
  /*判断匹配*/
  [data-4060="40"] & {
    font-size: $fontSize-40;
  }
  [data-4060="60"] & {
    font-size: $fontSize-60;
  }
}

@mixin width_168_auto($width) {
  width: $width;
  /*判断匹配*/
  [data-168-auto="168"] & {
    width: $width-168;
  }
  [data-168-auto="auto"] & {
    width: $width-auto;
  }
}

@mixin width_auto_100($width) {
  width: $width;
  /*判断匹配*/
  [data-auto-100="100"] & {
    width: $width-100;
  }
  [data-auto-100="auto"] & {
    width: $width-auto;
  }
}
