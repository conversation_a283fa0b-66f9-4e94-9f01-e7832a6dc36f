import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "../src/assets/css/reset.css";
import "../src/assets/js/rem";
import { buryPoint } from "@/util/index";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import footerInfo from "@/views/components/footer.vue";
// import "@/assets/css/public.scss";
// import uniSDK from '../src/assets/js/wechatTest'//测试
// import '../src/assets/js/openIcon'//正式
// setRemInit();
import Vant from "vant";
import dayjs from "dayjs";
Vue.prototype.$buryPoint = buryPoint;
Vue.prototype.dayjs = dayjs;
import "vant/lib/index.css";
Vue.component("footerInfo", footerInfo);
Vue.use(Vant);
Vue.config.productionTip = false;
new Vue({
  router,
  store,
  render: h => h(App),
}).$mount("#app");
