<template>
  <div></div>
</template>

<script>
export default {
  beforeRouteEnter(to, from, next) {
    // 移除ZWJSBridge依赖，直接进行路由跳转
    console.log("从", from.name, "跳转到transfer页面");
    next();
  },
  created() {
    // 移除ZWJSBridge.openLink，使用标准的window.location进行跳转
    // 注意：这个URL需要根据新的独立部署环境进行调整
    const redirectUrl = "https://puser.zjzwfw.gov.cn/sso/mobile.do?action=oauth&scope=1&servicecode=BCDSGA_8886a94ce5a4569b7d5ef5f38e7bc9cb&redirectUrl=" +
                       encodeURIComponent(window.location.origin + "/index.html");

    console.log("准备跳转到:", redirectUrl);
    // 在独立部署环境中，可能需要根据实际情况调整跳转逻辑
    // window.location.href = redirectUrl;

    window.addEventListener(
      "pageshow",
      function (event) {
        console.log(
          "进入pageshow事件",
          event.persisted ||
            (window.performance && window.performance.navigation.type == 2)
        );
        if (
          event.persisted ||
          (window.performance && window.performance.navigation.type == 2)
        ) {
          console.log("页面从缓存中恢复");
          // 移除ZWJSBridge.close调用
        }
        // 检查login函数是否存在再调用
        if (typeof login === 'function') {
          login();
        }
      },
      false
    );
    const isWx = (isLoad = () => {}) => {
      var ua = navigator.userAgent.toLowerCase();
      if (ua.indexOf("micromessenger") !== -1) {
        console.log("html文件中在微信");
      } else {
        console.log("不在微信里");
        // 检查login函数是否存在再调用
        if (typeof login === 'function') {
          login();
        }
      }
    };
  },
  methods: {},
};
</script>

<style></style>
