<!--Author: 郭元扬
ProjectDescription: 授权成功页面
CreateTime: 2022-08-09
UpdateTime: 2022-08-09-->
<template>
  <div class="auth-success">
    <div class="content">
      <img src="@img/success.png" alt="" />
      <p>授权成功</p>
      <p style="font-size: 0.14rem; color: #686b73; margin-top: 0.14rem">
        {{ $route.query.serviceInfo.serviceName }}授权成功
      </p>
      <p class="tip" v-if="serviceInfo.infoLevel === '2'">
        {{ serviceInfo.serviceName }}从用户授权当日起，{{
          serviceInfo.accessDays
        }}天内有效，{{ serviceInfo.accessCount }}次使用
      </p>
      <p class="tip" v-else-if="serviceInfo.infoLevel === '1'">
        {{ serviceInfo.serviceName }}从用户授权当日起，{{
          serviceInfo.accessDays
        }}天内有效
      </p>
      <p class="tip" v-else-if="serviceInfo.infoLevel === '3'">
        {{ serviceInfo.serviceName }}一次授权，一次使用
      </p>
    </div>
    <!-- <div class="footer">
      <div class="footer-one">
        <van-button type="info" @click="goBack">返回首页</van-button>
      </div>
    </div> -->
  </div>
</template>

<script>
import common from "@/util/index";
export default {
  data() {
    return {
      serviceInfo: {},
    };
  },
  created() {
    this.createTime = new Date().getTime();
    this.serviceInfo = this.$route.query.serviceInfo;
  },
  mounted() {
    this.mountedTime = new Date().getTime();
    common.changeTitle("医保个人授权");
    this.$buryPoint(this.mountedTime - this.createTime);
  },
  methods: {
    goBack() {
      this.$router.push({
        path: "/home",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/css/public.scss";
.auth-success {
  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 4rem;
    background: white;
    img {
      width: 1.44rem;
      height: 1.44rem;
      margin-bottom: 0.24rem;
    }
    p {
      color: #363a44;
      font-weight: 400;
      font-size: 0.2rem;
      text-align: center;
    }
  }
  .footer {
    width: 100%;
    height: 0.71rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.28);
    position: fixed;
    bottom: 0;
    .footer-one {
      padding: 0.16rem;
      width: 90%;
      height: 0.48rem;
      margin: 0 auto;
      vertical-align: middle;
      position: relative;
      .van-button {
        position: absolute;
        width: 90%;
        border-radius: 0.24rem;
        height: 0.48rem;
        vertical-align: middle;
        line-height: 0.48rem;
        @include font_size_16_22($fontSize-16);
      }
    }
  }
  .tip {
    font-size: 0.28rem;
    color: #428ffc;
    line-height: 0.4rem;
    margin-top: 0.14rem;
  }
}
</style>
