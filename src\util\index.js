/*
2021-12-06 Belongs to the work
yangwenyang
PS 处理时间需要安装dayjs 
// cnpm install dayjs --save
https://www.jianshu.com/p/ed20fb23e658 dayjs官网
*/
import dayjs from "dayjs";
import CryptoJS from "crypto-js";
import store from "@/store";
const common = {
  changeTitle(title) {
    // 移除ZWJSBridge依赖，直接设置页面标题
    document.title = title;
    console.log("页面标题已设置为:", title);
  },

  // 获取URL参数
  getUrlParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
  },

  // 获取URL hash参数
  getHashParam(name) {
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);
    return params.get(name);
  },

  // 从URL中获取token（支持query参数和hash参数）
  getTokenFromUrl() {
    // 先尝试从query参数获取
    let token = this.getUrlParam('token');
    if (!token) {
      // 再尝试从hash参数获取
      token = this.getHashParam('token');
    }
    if (!token) {
      // 尝试从完整URL中提取token参数
      const url = window.location.href;
      const tokenMatch = url.match(/[?&]token=([^&]+)/);
      if (tokenMatch) {
        token = decodeURIComponent(tokenMatch[1]);
      }
    }
    return token;
  },
  changeTitleBar(params) {
    if (window.ele_health_card) {
      // 传0都透明，1都白色
      window.ele_health_card.changeTitleBar(params);
    }
    if (window.webkit && window.navigator.userAgent.indexOf("JKTNATIVE") > 0) {
      this.callApp("changeTitleBar", params);
    }
  },
};
// ======= 处理时间
export const formatTime = (value, format = "YYYY-MM-DD hh:mm:ss") => {
  return dayjs(value).format(format); //时间戳转time YYYY-MM-DD hh:mm:ss
};
export function changeDateFormat(format = "YYYYMMDD") {
  return dayjs(new Date()).format(format); //当前时间YYYY-MM-DD
}
export function getToday(format = "YYYY年MM月DD日 hh:mm:ss") {
  return dayjs(new Date()).format(format); //当前时间YYYY-MM-DD hh:mm:ss
}
export function getYT(format = "YYYY年MM月") {
  return dayjs(new Date()).format(format); //当前时间YYYY-MM-DD hh:mm:ss
}
export function backDayRange(format = "YYYYMMDD") {
  return dayjs(new Date()).subtract(2, "day").format(format); //倒退几天 减几天就是倒退几天
}
export function lastMonth(format = "YYYYMM") {
  var currentdate = dayjs(new Date()).subtract(1, "months").format(format);
  return currentdate; //当前时间的前1个月
}
export function twoMonth(format = "YYYYMM") {
  var currentdate = dayjs(new Date())
    .subtract(2, "day")
    .subtract(1, "months")
    .format(format);
  return currentdate; //当前时间的前1个月
}
export function dayMonth(format = "YYYYMM") {
  var currentdate = dayjs(new Date()).subtract(3, "day").format(format);
  return currentdate; //3号之前显示的是上个月 3号之后显示这个月
  //就是时间往前推3天就好  显示当月
}
export function timestamp() {
  return dayjs().unix(); //时间转时间戳
}
// 接受0(星期日)到6(星期六)的数字
export function lastWeekDay(format = "YYYY-MM-DD") {
  return dayjs().day(0).format(format); //定义到周日 上周日的时间
}
export function twoWeekDay(format = "YYYYMMDD") {
  //定义到周日 每周二更新上周日 否则是上上周日 就是周2之前是上上周日 周2更新显示上周日
  return dayjs(new Date()).subtract(3, "day").day(0).format(format);
}
// ====== 保留小数
export function toDecimal2(x) {
  // 保留2位小数点 强制保留2位小数，如：2，会在2后面补上00.即2.00
  // 这个是四舍五入的 3.245 变成3.25
  var f = parseFloat(x);
  if (isNaN(f)) {
    return false;
  }
  var f = Math.round(x * 100) / 100;
  var s = f.toString();
  var rs = s.indexOf(".");
  if (rs < 0) {
    rs = s.length;
    s += ".";
  }
  while (s.length <= rs + 2) {
    s += "0";
  }
  return s;
}
export function toFixed(num, decimal) {
  //  保留2位小数点 这个不是四舍五入
  // 2个参数 第一个是数值 第二个是保留几位
  num = num.toString();
  let index = num.indexOf(".");
  if (index !== -1) {
    num = num.substring(0, decimal + index + 1);
  } else {
    num = num.substring(0);
  }
  return parseFloat(num).toFixed(decimal);
}
// ======= 小数转百分数 ======= 百分数转小数
export function toPercent(point) {
  // 小数转化为百分数  toFixed() 作用保留几位小数位 现在是保留2位
  var percent = Number(point * 100).toFixed(2);
  percent += "%";
  return percent;
}
export function toPoint(percent) {
  // 百分数转小数
  var str = percent.replace("%", "");
  str = str / 100;
  return str;
}
//随机字符串方法
export function randomString() {
  let len = 32;
  let chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  let maxPos = chars.length;
  let character = "";
  for (let i = 0; i < len; i++) {
    character += chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return character;
}
// URL链接后面的参数值(支持带# 不带#的)
export function paramObj(url) {
  const search = url.split("?")[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, " ") +
      '"}'
  );
}
export function judgeType(target, types) {
  const otc = Object.prototype.toString.call(target);
  const targetType = otc.replace(/^(\[object\s)|(])$/g, "").toLocaleLowerCase();
  if (typeof types === "string") {
    return targetType === types;
  }
  if (Array.isArray(types)) {
    return types.includes(targetType);
  }
  return targetType;
}
// 深拷贝
export function deepCopy(obj, cache) {
  if (judgeType(obj, "object")) {
    const newObj = Object.create(Object.prototype);
    const keyArr = Object.keys(obj); // Symbol不支持 可以使用Object.getOwnPropertySymbols() 或
    const keyArrSymbol = Object.getOwnPropertySymbols(obj);
    // const keyArr = Reflect.ownKeys(obj) // __ob__ 不适用需排除它
    if (cache) {
      cache.push(obj);
    }
    keyArr.forEach(v => {
      const curr = obj[v];
      if (cache) {
        if (!~cache.indexOf(curr)) {
          newObj[v] = deepCopy(obj[v], cache);
          return;
        }
        cache.push(curr);
        return;
      }
      newObj[v] = deepCopy(obj[v], cache);
    });
    keyArrSymbol.forEach(v => {
      const curr = obj[v];
      if (cache) {
        if (!~cache.indexOf(curr)) {
          newObj[v] = deepCopy(obj[v], cache);
          return;
        }
        return;
      }
      newObj[v] = deepCopy(obj[v], cache);
    });
    return newObj;
  }
  if (judgeType(obj, "array")) {
    if (cache) {
      if (!~cache.indexOf(obj)) {
        cache.push(obj);
        return obj.map(v => {
          return deepCopy(v, cache);
        });
      }
      return obj.length === 0 ? [] : null;
    }
    if (obj.length === 0) {
      return [];
    }

    return obj.map(v => {
      return deepCopy(v, cache);
    });
  }
  return obj;
}

// 提取两个数组相同id的对象
export function findObjectArray(targetArray, standerdArray, key) {
  var map1 = new Map();
  var arr = [];
  for (const item of targetArray) {
    map1.set(item[key], item);
  }
  for (const item of standerdArray) {
    if (map1.get(item)) {
      arr.push(map1.get(item));
    }
  }
  return arr;
}
/*
export function getQueryString(name) {
      //location.hash 这个是http网址
      const hash = decodeURIComponent(location.hash)
      const queryArr = hash.slice(hash.indexOf('?') + 1).split('&')
      let result = ''
      queryArr.forEach(item => {
        const [key, value] = item.split('=')
        if (key === name) {
          result = value
        }
      })
     return result
    }
   这个封装的
   以下引入封装的代码块
*/
// 替代ZwLog的埋点函数（独立部署版本）
export function buryPoint(t2) {
  // 移除ZwLog依赖，使用自定义的日志记录
  console.log('页面埋点统计:', {
    miniAppId: "2002243178",
    miniAppName: "医保个人授权",
    loadTime: Number(t2) / 1000,
    timestamp: new Date().toISOString(),
    userInfo: store.state.app.userInfo ? {
      userId: store.state.app.userInfo.userid,
      userName: store.state.app.userInfo.username
    } : null,
    page: window.location.pathname,
    url: window.location.href
  });

  // 在独立部署环境中，可以将日志发送到自己的统计服务
  // 例如：发送到自定义的日志收集接口
  if (process.env.NODE_ENV === 'production') {
    // 可以在这里添加发送到自定义日志服务的逻辑
    // fetch('/api/analytics', { method: 'POST', body: JSON.stringify(logData) })
  }
}

// 脱敏加密
export function encrypt(str, key_ = "hfdjk670qEH5lm3b") {
  if (str) {
    if (typeof str == "object") {
      str = JSON.stringify(str);
    } //预约挂号：jfdjk670qEH5lm3a   实名认证：hfdjk670qEH5lm3b
    let key = CryptoJS.enc.Utf8.parse(key_);
    let srcs = CryptoJS.enc.Utf8.parse(str);
    let encrypted = CryptoJS.AES.encrypt(srcs, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString();
  } else {
    return "";
  }
}

// 姓名脱敏
export function noPassByName(str) {
  if (null != str && str != undefined) {
    if (str.length) {
      return "*" + str.substring(1, str.length);
    }
  } else {
    return "";
  }
}

export default common;
