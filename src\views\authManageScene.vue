<!--Author: 郭元扬
ProjectDescription: 个人医保服务授权管理（场景列表）
CreateTime: 2022-07-22
UpdateTime: 2022-07-22-->

<template>
  <div class="auth-manage-scene">
    <div class="service-name">
      <p>服务名称</p>
      <p>{{ $route.query.name }}</p>
    </div>
    <div class="tip">
      <div>
        <notice-bar :speed="10" :text="notice" />
      </div>
    </div>
    <div class="agreement" @click="() => (showAgree = true)">
      <div>
        <img src="@/assets/image/xieyi-icon.png" alt="" />
        <p>查看协议</p>
      </div>
      <img src="@/assets/image/detail.png" alt="" />
    </div>
    <div class="title">
      <p>序号</p>
      <p>场景名称</p>
      <p>授权管理</p>
    </div>
    <ul>
      <li
        class="scene-list"
        :style="{ backgroundColor: index % 2 === 1 ? '#F6F7F8' : '#ffffff' }"
        v-for="(item, index) of sceneList"
        :key="index"
      >
        <p>{{ index + 1 }}</p>
        <p>
          {{ item.sceneName }}
        </p>
        <p :class="{ red: item.status == '0' }" @click="revokeAuth(item)">
          {{ item.status == "0" ? "解除授权" : "未授权" }}
        </p>
      </li>
    </ul>
    <div class="footer">
      <div class="footer-one">
        <van-button
          :disabled="!sceneIds.length"
          @click="revokeAuth(1)"
          type="info"
          >解除该服务的所有授权</van-button
        >
      </div>
    </div>
    <!-- 协议组件 -->
    <agreement
      v-if="showAgree"
      :show="showAgree"
      :agreements="agreements1.concat(agreements2)"
      agreementName="1"
      @agreeEmit="agreeEmit"
    />
  </div>
</template>

<script>
import noticeBar from "@/components/noticeBar.vue";
import common, { randomString, encrypt } from "@/util/index";
import { Dialog, Notify } from "vant";
import {
  getAuthList,
  evictAuth,
  getOtherAgreements,
  getScenes,
  getAgreements,
  getServiceList,
} from "@/api";
import agreement from "@/views/components/agreements.vue";
export default {
  components: { agreement, noticeBar },
  data() {
    return {
      notice: "",
      sceneList: [], // 授权生效中status=0的场景id
      allSceneIds: [], // 该服务下的所有场景id
      sceneIds: [],
      showAgree: false,
      agreements1: [], // 场景协议内容
      agreements2: [], //非场景协议内容
      allSceneLinkAgrmts: [],
      serviceInfo: {},
    };
  },
  watch: {},
  methods: {
    // 获取服务信息
    handleGetServiceList() {
      getServiceList({
        current: 1,
        size: 10,
        logTraceID: randomString(),
        serviceIds: [this.$route.query.id],
      }).then(res => {
        this.serviceInfo = res.records[0];
        if (this.serviceInfo.infoLevel === "2") {
          this.notice = `${this.serviceInfo.serviceName}从用户授权当日起，${this.serviceInfo.accessDays}天内有效，${this.serviceInfo.accessCount}次使用`;
        } else if (this.serviceInfo.infoLevel === "1") {
          this.notice = `${this.serviceInfo.serviceName}从用户授权当日起，${this.serviceInfo.accessDays}天内有效`;
        } else if (this.serviceInfo.infoLevel === "3") {
          this.notice = `${this.serviceInfo.serviceName}一次授权，一次使用`;
        }
      });
    },
    // 关闭弹出层
    agreeEmit() {
      this.showAgree = false;
    },
    // 解除服务下的场景授权
    handleChannService() {
      const userEvictServices = [];
      for (const item of this.sceneList) {
        userEvictServices.push({
          serviceId: this.$route.query.id,
          sceneIds: [item.sceneId],
        });
      }
      let params = {
        logTraceID: randomString(),
        userEvictServices: userEvictServices,
        // serviceIds: [this.$route.query.id],
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
      };
      channService(params).then(res => {
        if (res.status == 200) {
          Notify({ type: "success", message: "解除成功" });
          this.getSceneList();
          // this.$router.push({
          //   path: "/authManage",
          // });
        }
      });
    },
    // 获取场景列表
    getSceneList() {
      const data = {
        logTraceID: randomString(),
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
      };
      if (this.$route.query.business) {
        data.certNo = encrypt(this.$route.query.business);
      }
      getAuthList(data).then(res => {
        var obj = {};
        var arr = [];
        res.records.reduce((pre, cur) => {
          if (!obj[cur.serviceId]) {
            obj[cur.serviceId] = [cur];
            arr.push(cur);
          } else {
            obj[cur.serviceId].push(cur);
          }
        }, []);
        this.sceneList = obj[this.$route.query.id];
        this.sceneIds = []; // 授权生效中status=0的场景id
        this.allSceneIds = []; // 该服务下的所有场景id
        for (const item of this.sceneList) {
          this.allSceneIds.push(item.sceneId);
          if (item.status == "0") this.sceneIds.push(item.sceneId);
        }
        this.handleGetSceneAgreements();
        this.handleGetOtherAgreements();
      });
    },
    // 获取场景再根据场景绑定的协议id获取场景协议内容
    handleGetSceneAgreements() {
      getScenes({
        logTraceID: randomString(),
        status: "0",
        sceneIds: this.allSceneIds,
        serviceId: this.$route.query.id,
      }).then(res => {
        this.allSceneLinkAgrmts = [];
        for (const item of res.records) {
          if (item.sceneLinkAgrmts) {
            this.allSceneLinkAgrmts = this.allSceneLinkAgrmts.concat(
              item.sceneLinkAgrmts.split(",")
            );
          }
        }
        // 获取场景协议
        getAgreements({
          // 协议id为空主动塞入0
          agrmtIds: this.allSceneLinkAgrmts.length
            ? this.allSceneLinkAgrmts
            : ["0"],
          logTraceID: randomString(),
        }).then(res => {
          this.agreements1 = res.records;
        });
      });
    },
    // 获取非场景协议内容
    handleGetOtherAgreements() {
      getOtherAgreements({
        logTraceID: randomString(),
        sceneIds: this.allSceneIds,
        serviceId: this.$route.query.id,
      }).then(res => {
        this.agreements2 = JSON.parse(res.data).nonSceneAgrmts;
      });
    },
    // 解除授权
    handleRevokeAuth(ids) {
      const data = {
        serviceIds: [this.$route.query.id],
        sceneIds: ids,
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
        logTraceID: randomString(),
      };
      if (this.$route.query.business) {
        data.certNo = encrypt(this.$route.query.business);
      }
      evictAuth(data).then(res => {
        console.log(data, "dadadada");
        if (res.status == 200) {
          Notify({ type: "success", message: "解除成功" });
          this.getSceneList();
        }
      });
    },
    revokeAuth(flag) {
      if (flag.status && flag.status === "1") return false;
      Dialog.confirm({
        title: "解除授权确认",
        message: "是否确认解除？",
        confirmButtonText: "解除",
        confirmButtonColor: "#255BDA",
        width: "2.79rem",
        className: "Diy",
      })
        .then(() => {
          if (flag === 1) {
            this.handleRevokeAuth(this.sceneIds);
          } else {
            this.handleRevokeAuth([flag.sceneId]);
          }
          // on confirm
        })
        .catch(() => {
          // on cancel
        });
    },
  },
  created() {
    this.createTime = new Date().getTime();
    this.getSceneList();
    this.handleGetServiceList();
  },
  mounted() {
    this.mountedTime = new Date().getTime();
    common.changeTitle("医保个人授权");
    this.$buryPoint(this.mountedTime - this.createTime);
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/css/public.scss";
.auth-manage-scene {
  background-color: #f6f7f8;
  .service-name {
    width: 100%;
    height: 0.83rem;
    background: url("../assets/image/shouquanguanli-bgp.png") 100% 100%;
    background-size: 100% 100%;
    p {
      color: white;
      margin-left: 0.16rem;
      &:nth-of-type(1) {
        @include font_size_12_18($fontSize-12);
        padding-top: 0.19rem;
        padding-bottom: 0.18rem;
      }
      &:nth-of-type(2) {
        @include font_size_16_22($fontSize-16);
      }
    }
  }
  .tip {
    div {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 0.27rem;
      background-color: #e7f3ff;
    }
    & > div {
      margin-bottom: 0.1rem;
    }
    img {
      width: 0.11rem;
      height: 0.1rem;
      margin-left: 0.16rem;
      // margin-right: 0.09rem;
    }
    .tip-text {
      flex: 1;
      width: 100%;
    }
    p {
      @include font_size_12_18($fontSize-12);
      color: #428ffc;
      transform: scale(0.92);
    }
  }
  .agreement {
    width: 100%;
    height: 0.55rem;
    margin-bottom: 0.1rem;
    background-color: white;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.16rem;
    & > div {
      display: flex;
      align-items: center;
      img {
        width: 0.15rem;
        height: 0.15rem;
        margin-right: 0.11rem;
      }
    }
    & > img {
      width: 0.06rem;
      height: 0.12rem;
    }
  }
  .red {
    color: red !important;
  }
  p {
    @include font_size_14_20($fontSize-14);
  }
  .title {
    box-sizing: border-box;
    width: 100%;
    line-height: 0.56rem;
    background: #e9f3fe;
    display: flex;
    padding: 0 0.16rem;
    p {
      font-weight: 500;
      color: #363a44;
      &:nth-of-type(1) {
        flex: 2;
      }
      &:nth-of-type(2) {
        flex: 5;
      }
      &:nth-of-type(3) {
        flex: 3;
      }
    }
  }
  .scene-list {
    box-sizing: border-box;
    width: 100%;
    display: flex;
    line-height: 0.28rem;
    height: 0.56rem;
    align-items: center;
    padding: 0 0.16rem;
    p {
      color: #363a44;
      &:nth-of-type(1) {
        flex: 2;
      }
      &:nth-of-type(2) {
        flex: 5;
      }
      &:nth-of-type(3) {
        flex: 3;
      }
    }
  }
  .footer {
    width: 100%;
    height: 0.71rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.28);
    position: fixed;
    bottom: 0;
    .footer-one {
      padding: 0.16rem;
      width: 90%;
      height: 0.48rem;
      margin: 0 auto;
      vertical-align: middle;
      position: relative;
      .van-button {
        position: absolute;
        width: 90%;
        border-radius: 0.24rem;
        height: 0.48rem;
        vertical-align: middle;
        line-height: 0.48rem;
        @include font_size_16_22($fontSize-16);
      }
    }
  }
}
</style>
