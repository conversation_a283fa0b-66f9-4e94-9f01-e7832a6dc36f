/**
 * 用户认证工具类
 * 处理Token获取用户信息的逻辑
 */

import { getUserInfoByTokenFromCsaf } from "@/api/index";
import { randomString } from "@/util/index";
import store from "@/store";

/**S
 * 从URL中获取token参数
 * @returns {string|null} token值或null
 */
export function getTokenFromUrl() {
  // 先尝试从query参数获取
  const urlParams = new URLSearchParams(window.location.search);
  let token = urlParams.get('accessToken');
  
  if (!token) {
    // 再尝试从hash参数获取
    const hash = window.location.hash.substring(1);
    const hashParams = new URLSearchParams(hash);
    token = hashParams.get('accessToken');
  }
  
  if (!token) {
    // 尝试从完整URL中提取token参数
    const url = window.location.href;
    const tokenMatch = url.match(/[?&]accessToken=([^&]+)/);
    if (tokenMatch) {
      token = decodeURIComponent(tokenMatch[1]);
    }
  }
  
  return token;
}

/**
 * 通过Token获取用户信息并存储到Vuex
 * @param {string} token - 用户token
 * @returns {Promise<Object>} 用户信息对象
 */
export async function fetchUserInfoByToken(token) {
  try {
    console.log("开始通过Token获取用户信息:", token);

    const response = await getUserInfoByTokenFromCsaf({
      accessToken: token,
      logTraceID: randomString(),
    });
    console.log("data",response)
    if (response.data ) {
      const data = JSON.parse(response.data);
    

      // 根据实际返回的数据结构调整字段映射
      const userInfo = {
        username: data.psnName,
        idnum: data.certno,
        userid: data.psnNo,
        idType: data.psnCertType,
        phone: data.mob,
        ...data // 保留其他字段
      };
      
      console.log("用户信息获取成功:", userInfo);
      
      // 存储到Vuex
      store.commit("app/setUserInfo", userInfo);
      
      return userInfo;
    } else {
      throw new Error(`获取用户信息失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error("获取用户信息异常:", error);
    throw error;
  }
}

/**
 * 检查用户是否已登录（检查Vuex中是否有用户信息）
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn() {
  const userInfo = store.state.app.userInfo;
  return userInfo && userInfo.userid && userInfo.username;
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息对象或null
 */
export function getCurrentUserInfo() {
  return store.state.app.userInfo || null;
}

/**
 * 清除用户信息
 */
export function clearUserInfo() {
  store.commit("app/setUserInfo", {});
  window.sessionStorage.removeItem("userInfo");
}

/**
 * 用户认证中间件 - 在页面组件中使用
 * @param {Object} options - 配置选项
 * @param {Function} options.onSuccess - 认证成功回调
 * @param {Function} options.onError - 认证失败回调
 * @param {boolean} options.required - 是否必须认证，默认true
 */
export async function userAuthMiddleware(options = {}) {
  const {
    onSuccess = () => {},
    onError = () => {},
    required = true
  } = options;
  
  try {
    // 1. 检查是否已经有用户信息
    if (isUserLoggedIn()) {
      console.log("用户已登录，直接使用现有信息");
      onSuccess(getCurrentUserInfo());
      return;
    }
    
    // 2. 尝试从URL获取token
    const token = getTokenFromUrl();
    
    if (token) {
      // 3. 通过token获取用户信息
      console.log("fetchUserInfoByToken");
      const userInfo = await fetchUserInfoByToken(token);
      onSuccess(userInfo);
      console.log("userInfo");
    } else if (required) {
      // 4. 如果必须认证但没有token，触发错误
      const error = new Error("未找到用户认证Token");
      onError(error);
    } else {
      // 5. 不是必须认证，返回空用户信息
      onSuccess(null);
    }
  } catch (error) {
    console.error("用户认证失败:", error);
    onError(error);
  }
}

/**
 * 安全获取用户身份证号码（脱敏处理）
 * @param {Object} userInfo - 用户信息对象
 * @returns {string} 脱敏后的身份证号码
 */
export function getMaskedIdNumber(userInfo = null) {
  const user = userInfo || getCurrentUserInfo();
  
  if (user && user.idnum && typeof user.idnum === 'string') {
    return user.idnum.replace(
      /^(.{1})(?:\w+)(.{1})$/,
      "$1****************$2"
    );
  }
  
  return "****************"; // 默认值
}
