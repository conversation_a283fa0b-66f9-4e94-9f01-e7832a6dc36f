{"name": "lx", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve-dev": "vue-cli-service serve --mode serve-dev", "build": "vue-cli-service build"}, "dependencies": {"@aligov/jssdk-mgop": "^3.1.9", "axios": "^0.25.0", "callapp-lib": "^3.5.3", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "node-sass": "^6.0.1", "sass-loader": "^10.2.0", "sm-crypto": "^0.3.12", "vant": "^2.12.33", "vconsole": "^3.14.6", "vue": "^2.6.11", "vue-loader": "^17.0.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "vue-template-compiler": "^2.6.11"}}