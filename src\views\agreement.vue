<template>
  <div class="main">
    <div class="header"></div>
    <div class="sec">
      <ul>
        <li v-for="(item, index) in accreditList.accreditList" :key="index">
          {{ item.title }}
        </li>
      </ul>
    </div>
    <div class="footer-title">本服务由杭州市医保局提供</div>
  </div>
</template>
<script>
import common from "../util/index";
import { chanLimitinfo } from "../api/index";
export default {
  data() {
    return {
      accreditList: [],
    };
  },
  created() {
    this.checkProtocol();
  },
  methods: {
    //查看服务协议
    checkProtocol() {
      let params = {
        userMark: "33333",
        // serviceId:this.$route.query.serviceId,
        serviceId: 1,
      };
      chanLimitinfo(params).then(res => {
        this.accreditList = JSON.parse(
          JSON.parse(res.data.data).limitAuthContent
        );
      });
    },
  },
  mounted() {
    common.changeTitle("医保个人授权");
  },
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  background: url("../assets/image/beijing2.png") no-repeat center center;
  background-size: 100% 100%;
  .header {
    width: 100%;
    height: 0.78rem;
    background: url("../assets/image/shouquanxieyi.png") no-repeat center center;
    background-size: 100% 100%;
  }
  .sec::-webkit-scrollbar {
    width: 0;
  }
  .sec {
    width: 95%;
    margin: -20px auto;
    height: 5rem;
    background: url("../assets/image/juxing1.png") no-repeat center center;
    background-size: 100% 100%;
    overflow-y: scroll;
    ul {
      padding: 0.16rem;
      li {
        color: #666666;
        font-size: 0.14rem;
        line-height: 0.21rem;
      }
      :first-child {
        color: #2a73fd;
        font-size: 0.16rem;
        margin-bottom: 0.1rem;
      }
    }
  }
  .footer-title {
    position: fixed;
    bottom: 0.2rem;
    padding: 0.2rem;
    font-size: 0.12rem;
    text-align: center;
    width: 80%;
    letter-spacing: 0.02rem;
    height: 0.115rem;
    color: #ffff;
    font-family: AlibabaPuHuiTiR;
  }
}
</style>
