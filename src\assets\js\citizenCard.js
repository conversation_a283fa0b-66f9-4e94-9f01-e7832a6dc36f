! function() {
	var i = [{
		funName: "youDunFaceDetection",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "youDunIDCard",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "alert",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getAllDeviceInfo",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getUserInfo",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getAccessNumber",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "config",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "userLogin",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "contactCustomerService",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "openNativeModule",
		param: !0,
		reFun: !0,
		staticType: 0
	},{
		funName: "cpScanCode",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "scanCode",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "chooseImage",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "chooseVideo",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getLocation",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "choosePhoneContact",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "pay",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "modifyPayPwd",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "addBankCard",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "addHmhfCard",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "requireAuth",
		param: !0,
		reFun: !0,
		staticType: 0
	},{
		funName: "openWXMiniProgram",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getAccessToken",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "faceDetection",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "ocrIDCard",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "closeWebview",
		param: !1,
		reFun: !1,
		staticType: 0
	}, {
		funName: "getAuthcode",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "startVoice",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "endVoice",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "faceDetection",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "setTitle",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "loginOut",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "openMap",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "share",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "makePhoneCall",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "alert",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "evaluate",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "confirmAlert",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "showToast",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "showLoading",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "hideLoading",
		param: !1,
		reFun: !1,
		staticType: 0
	}, {
		funName: "showActionSheet",
		param: !0,
		reFun: !0,
		staticType: 1
	}, {
		funName: "setTitleBarText",
		param: !0,
		reFun: 0,
		staticType: 0
	}, {
		funName: "setTitleBarLeftButton",
		param: !0,
		reFun: !0,
		staticType: 1
	}, {
		funName: "setTitleBarRightButton",
		param: !0,
		reFun: !0,
		staticType: 1
	}, {
		funName: "downloadfile",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "collection",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getDeviceInfo",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getZoneInfo",
		param: !0,
		reFun: !0,
		staticType: 0
	}, {
		funName: "getNetworkInfo",
		param: !0,
		reFun: !0,
		staticType: 0
	},{
		funName: "openSocialCard",//电子社保卡相关API
		param: !0,
		reFun: !0,
		staticType: 0
	},{
		funName: "openThridApp", //打开第三方APP
		param: !0,
		reFun: !0,
		staticType: 0
	},{
		funName: "noticeApp",  //将消息通知给APP
		param: !0,
		reFun: 0,
		staticType: 0
	},{
		funName: "chooseImageBase64", //单独唤起照片或者摄像头
		param: !0,
		reFun: !0,
		staticType: 0
	},{
		funName: "huaweiNFC", //华为NFC相关功能
		param: !0,
		reFun: 	!0,
		staticType: 0
	},{
		funName: "saveDesktop", //保存到桌面
		param: !0,
		reFun: 	0,
		staticType: 0
	},{
		funName: "brightness", //屏幕调亮
		param: !0,
		reFun: 	0,
		staticType: 0
	},{
		funName: "leqi", //乐琪证件照
		param: !0,
		reFun: !0,
		staticType: 0
	}
	];
	window.yl = {
		loaded: 0,
		readyType: 0,
		readyList: {},
		ua:window.navigator.userAgent,
		getSystemInfo: function() {
			var e = window.yluareplace,
				a = {
					h5ContainerVersion: void 0,
					appVersion: void 0,
					systemId: void 0,
					appPlatform: void 0
				};
			if(e) {
				try {
					e = JSON.parse(e)
				} catch(e) {
					console.log(e)
				}
				a.appVersion = e.appVersion||e.app_version, a.h5ContainerVersion = e.h5ContainerVersion, a.systemId = e.systemId, a.appPlatform = e.appPlatform
			} else console.warn("获取容器版本错误");
			return a
		},
		hasReady: function() {
			console.log("event ready!");
			var e = window.yl.readyList;
			for(var a in e) window.jsbridge.add(e[a]), delete window.yl.readyList[a]
		},
		readydo: function() {},
		ready: function(e) {
			this.readydo = e
		},
		call: function(e, a, n) {
			console.log("方法构造");
			var t = function(e) {
				for(var a = 0; a < i.length; a++) {
					var n = i[a];
					if(e === n.funName) return n
				}
				return !1
			}(e);
			if(!t) return console.warn("非法调用"), !1;
			if(t.param && (t.param = a), t.reFun) {
				if(!n) return console.log("需要传入回调"), console.warn("需要传入回调");
				t.reFun = n
			}
			if(window.jsbridge) {window.jsbridge.add(t);}
			else {
				// console.error("time"+ (new Date).getTime());
				var r = "ListItem" + (new Date).getTime()+Math.ceil(Math.random()*100);
				this.readyList[r] = t
			}
		}
	}
}();