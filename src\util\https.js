import axios from "axios";
import { Toast } from "vant";
import { randomString } from "@/util/index";
import { Notify } from "vant";
import store from "@/store";

// // 请求错误收集
function addErrorLog(info) {
    const {
        message,
        name,
        config: { url, baseURL, headers, params, data, method },
        status,
        statusText,
    } = info;
    const errData = {
        type: "ajax",
        message,
        name,
        href: window.location.href,
        status,
        statusText,
        url,
        baseURL,
        headers: JSON.stringify(headers),
        params,
        data,
        method,
    };
    store.dispatch("app/collectError", errData);
}
//创建axios,赋给变量service
// const service = axios.create({
//   baseURL: "/capi",
//   // timeout: 10000,//超时
//   headers: {
//     "Content-Type": "application/json",
//     channelCode: "userView",
//     logTraceId: randomString(),
//     sign: 1,
//   },
// });
const showStatus = status => {
    let message = "";
    switch (status) {
        case 400:
            message = "请求错误(400)";
            break;
        case 401:
            message = "未授权，请重新登录(401)";
            break;
        case 402:
            message = "拒绝访问(402)";
            break;
        case 404:
            message = "请求出错(404)";
            break;
        case 408:
            message = "请求超时(408)";
            break;
        case 500:
            message = "服务器错误(500)";
            break;
        case 501:
            message = "服务未实现(501)";
            break;
        case 502:
            message = "网络错误(502)";
            break;
        case 503:
            message = "服务不可用(503)";
            break;
        case 504:
            message = "网络超时(504)";
            break;
        case 505:
            message = "HTTP版本不受支持(505)";
            break;
        default:
            message = `连接出错(${status})!`;
    }
    return `${message}，请检查网络或联系管理员！`;
};
// 请求拦截器
axios.interceptors.request.use(
    config => {
        Toast.loading({
            message: "拼命加载中...",
            forbidClick: true,
        });
        return config;
    },
    err => {
        err.message = "服务器异常，请联系管理员！";
        // 错误抛到业务代码
        return Promise.reject(err);
    }
);

// 响应拦截器
axios.interceptors.response.use(
    response => {
        const status = response.status;
        const res = response.data;
        Toast.clear();
        // if (Number(res.status) !== 200)
        //   Notify({ type: "danger", message: res.message });
        let msg = "";
        if (status < 200 || (status >= 300 && status != 401 && status != 500)) {
            // 处理http错误，抛到业务代码
            msg = showStatus(status);
            if (typeof response.data === "string") {
                response.data = { msg };
            } else {
                response.data.msg = msg;
            }
            return response;
        } else if (status == 200) {
            return response;
        } else if (status == 500) {
            msg = showStatus(status);
            response.data = { msg: msg };
            router.replace({ name: "exception", query: { type: 500 } });
            return response;
        }
    },
    err => {
        Notify({
            type: "danger",
            message: "请求超时或服务器异常，请检查网络或联系管理员！",
        });
        addErrorLog(err.response);
        // err.message = "请求超时或服务器异常，请检查网络或联系管理员！";
        return Promise.reject(err);
    }
);

class HttpRequest {
    constructor(baseURL, apiPath) {
        this.apiPath =
            apiPath[apiPath.length - 1] !== "/" ? apiPath + "/" : apiPath;
        this.baseURL = baseURL + this.apiPath;
    }
    getInitConfig() {
        const config = {
            withCredentials: true, // 允许携带cookie
            timeout: 50000,
        };
        return config;
    }
    getApiUrl(options) {
        const baseURL = options.baseURL;
        const apiPath = this.apiPath;
        let url = options.url;
        if (url.indexOf("/")) {
            url = apiPath + url;
        }
        options.baseURL = baseURL ? baseURL : this.baseURL;
        options.url = url;
        return options;
    }
    arrageConfig(options) {
        options = this.getApiUrl(options);
        const method = options.method;
        const baseURL = options.baseURL;
        const url = options.url;
        const headers = options.headers || {};
        let data = options.data || {};
        const config = {
            method,
            baseURL,
            url,
            headers: {
                ...headers,
            },
        };
        if (options.timeout) {
            config.timeout = options.timeout;
        }
        if (method === "get") {
            config.params = data;
        } else {
            data =
                headers["Content-Type"] === "application/x-www-form-urlencoded"
                    ? qs.stringify(data)
                    : data;
            config.data = data;
        }
        return config;
    }
    getRequestOptions(option, data, other) {
        let baseURL;
        let url;
        let timeout;
        let responseType = "json";
        let headers = { ...other.headers };
        let notLogin = false;
        if (typeof option === "string") {
            url = option;
        } else {
            baseURL = option.baseURL;
            timeout = option.timeout;
            url = option.url;
            responseType = option.responseType || responseType;
            headers = option.headers ? { ...headers, ...option.headers } : headers;
            // eslint-disable-next-line no-param-reassign
            data = option.data;
            notLogin = option.notLogin || notLogin;
        }
        const options = {
            url,
            notLogin,
            data,
            method: other.method,
            baseURL,
            timeout,
            responseType,
            headers,
        };
        return options;
    }
    async request(options) {
        const {
            url,
            data,
            headers,
            method,
            baseURL,
            responseType,
            timeout,
            notLogin,
        } = options;
        // if (!notLogin && )
        // if (!notLogin && !store.state.user.token && !store.state.user.source) {
        //   // 需要登录且没有token
        //   return { code: 4008, message: "未登录" };
        // }
        const config = Object.assign(
            this.getInitConfig(),
            this.arrageConfig({
                baseURL,
                url,
                data,
                headers,
                method,
                timeout,
                notLogin,
                responseType,
            })
        );
        const res = await axios(config);
        const resData = res.data;
        return resData;
    }
    async postData(option, data) {
        const headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept-Language": "charset=utf-8",
            "X-XSRF-TOKEN": "1",
        };
        const options = this.getRequestOptions(option, data, {
            headers,
            method: "post",
        });
        return this.request(options);
    }
    async postJson(option, data) {
        const headers = {
            "Content-Type": "application/json;charset=utf-8",
            "Accept-Language": "charset=utf-8",
            "X-XSRF-TOKEN": "1",
        };
        const options = this.getRequestOptions(option, data, {
            headers,
            method: "post",
        });
        return this.request(options);
    }
    async getQuery(option, data) {
        const headers = {
            "Accept-Language": "charset=utf-8",
            "X-XSRF-TOKEN": "1",
        };
        const options = this.getRequestOptions(option, data, {
            headers,
            method: "get",
        });
        return this.request(options);
    }
    async postFile(option, data) {
        const headers = {
            "Content-Type": "multipart/form-data",
            "Accept-Language": "charset=utf-8",
            "X-XSRF-TOKEN": "1",
        };
        const options = this.getRequestOptions(option, data, {
            headers,
            method: "post",
        });
        return this.request(options);
    }
}
// 恢复原有的网络配置，使用实际的生产环境地址
const httpReq = new HttpRequest('', "/mamp");
const httpReq2 = new HttpRequest('', "/csaf");
const httpReq3 = new HttpRequest('', "/mamp");
export { httpReq2, httpReq, httpReq3 };
// export default httpReq;

// 将service 导出
// export default service;
import { mgop } from "@aligov/jssdk-mgop";

export default function ({ url, data, type, headers = {} }) {
    Toast.loading({
        message: "拼命加载中...",
        forbidClick: true,
    });
    return new Promise((r, j) => {
        // 使用标准的axios请求替代mgop
        const apiUrl = "/mamp/1.0.0/" + url;

        // 检查是否为FormData，如果是则不设置Content-Type让浏览器自动设置
        const isFormData = data instanceof FormData;

        const requestHeaders = {
            "X-XSRF-TOKEN": "1",
            // isTestUrl: "1", // 测试环境加这个请求头，生产环境不能加
            "X-Requested-With": "XMLHttpRequest",
            "channelCode": "userView",
            "logTraceId": randomString(),
            "sign": 1,
            ...headers, // 合并传入的headers
        };

        // 如果不是FormData，则设置Content-Type为application/json
        if (!isFormData) {
            requestHeaders["Content-Type"] = "application/json";
        }

        axios({
            method: type,
            url: apiUrl,
            data: data,
            headers: requestHeaders,
        })
        .then(response => {
            Toast.clear();
            r(response.data);
        })
        .catch(error => {
            Toast.clear();
            console.error("API请求失败:", error);
            j(error);
            Toast.fail("服务出错了");
        });
    });
}

export const service2 = function ({ url, data, type }) {
    Toast.loading({
        message: "拼命加载中...",
        forbidClick: true,
    });
    return new Promise((r, j) => {
        // 使用标准的axios请求替代mgop
        const apiUrl = "/csaf/" + url;

        axios({
            method: type,
            url: apiUrl,
            data: data,
            headers: {
                "X-XSRF-TOKEN": "1",
                // isTestUrl: "1", // 测试环境加这个请求头，生产环境不能加
                // Cookie: "XSRF-TOKEN=1",
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "channelCode": "userView",
                "logTraceId": randomString(),
                "sign": 1,
            },
        })
        .then(response => {
            Toast.clear();
            r(response.data);
        })
        .catch(error => {
            Toast.clear();
            console.error("API请求失败:", error);
            j(error);
            Toast.fail("服务出错了");
        });
    });
};
