// 解密方法
import CryptoJS from "crypto-js";
let key = CryptoJS.enc.Utf8.parse("hzsmkWebJsBridge"); //秘钥
const iv = CryptoJS.enc.Utf8.parse(""); //十六位十六进制数作为密钥偏移量
export function decryptH5(word) {
  let encryptedHexStr = CryptoJS.enc.Hex.parse(word);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decrypt = CryptoJS.AES.decrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

// 加密鉴权
import SmCrypto from "sm-crypto";
export function enCrypto(content) {
  let sm = require("sm-crypto").sm3;
  return sm(content);
}
