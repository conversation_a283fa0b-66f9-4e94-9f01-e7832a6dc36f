<template>
  <div class="mortgage">
    <div class="button">
      <van-button type="primary" @click="onSubmit">开始验证</van-button>
    </div>
  </div>
</template>
<script>
import uniSDK from "../../src/assets/js/wechatTest";
import CryptoJS from "../util/crypto";//解密
export default {
  data() {
    return {
      checked: false,
      listFace:"",
    };
  },
  created(){
      this.humanFace();
      this.OCRbusiness();
      this.humanbusiness();
  },
  methods: {
    //点击提交审核
    onSubmit() {
      uniSDK.faceIDInit({
        appId: "",
        idcard_number: "",
        idcard_name: "",
        web_title: "人脸识别",
        biz_no: "",
        sceneNo: "2", //2是h5
        providerName: "",
        sceneToken: "",
        return_url: "",
      });
    },
    // 人脸及OCR获取token
  },
};
</script>
