<!--Author: 郭元扬
ProjectDescription: 协议组件
CreateTime: 2022-07-21
UpdateTime: 2022-07-21-->

<template>
  <div class="agreement">
    <!-- <div style="height: 40vh">
      <van-tabs
        v-model="active"
        color="#428FFC"
        title-active-color="#428FFC"
        title-inactive-color="#363A44"
        line-height="2px"
        line-width="55px"
        :ellipsis="false"
        :before-change="beforeChange"
      >
        <van-tab v-for="(item, index) of tabs" :key="index">
          <template #title
            ><p class="agreement-title">
              {{ item.agrmtName }}
            </p></template
          >
          <div class="agreement-content" @scroll="handlescroll">
            <div class="content">
              <p class="ql-editor" v-html="item.agrmtDscy"></p>
            </div>
          </div>
        </van-tab>
      </van-tabs> -->
    <!-- <p class="ql-editor" v-html="tabs[0].agrmtDscy"></p> -->
    <!-- </div> -->
    <van-popup
      v-model="show"
      position="bottom"
      round
      :style="{ height: '75vh' }"
      @click-overlay="clickOverlay"
      :lock-srcoll="false"
      :close-on-click-overlay="false"
    >
      <div class="agreement-content">
        <div class="content">
          <p class="ql-editor" v-html="tabs[0].agrmtDscy"></p>
        </div>
      </div>
      <!-- <van-tabs
        v-model="active"
        color="#428FFC"
        title-active-color="#428FFC"
        title-inactive-color="#363A44"
        line-height="2px"
        line-width="55px"
        :ellipsis="false"
        :before-change="beforeChange"
      >
        <van-tab v-for="(item, index) of tabs" :key="index">
          <template #title
            ><p class="agreement-title">
              {{ item.agrmtName }}
            </p></template
          >
          <div class="agreement-content" @scroll="handlescroll">
            <div class="content">
              <p class="ql-editor" v-html="item.agrmtDscy"></p>
            </div>
          </div>
        </van-tab>
      </van-tabs>
      <div class="bottom" v-if="tabs.length && showBtn">
        <van-button plain type="info" @click="refuse">不同意</van-button>
        <van-button
          type="info"
          :disabled="!tabs[active].status"
          @click="readAndAgree"
          >我已阅读并同意</van-button
        >
      </div> -->
    </van-popup>
  </div>
</template>

<script>
import { randomString } from "@/util/index";
import { getAgreements } from "@/api";
import Vconsole from "vconsole";
export default {
  props: {
    showBtn: {
      type: Boolean,
      default() {
        return false;
      },
    },
    show: {
      type: Boolean,
      default() {
        return true;
      },
    },
    agreementName: {
      type: String,
      default: () => "",
    },
    ids: {
      type: String,
      default: () => "",
    },
    agreements: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      active: 0,
      tabs: [
        {
          agrmtDscy: `<p class="ql-align-right">尊敬的用户：</p><p class="ql-align-center"><strong class="ql-size-large"><em><u>申请人：&nbsp;证件号码：&nbsp;&nbsp;&nbsp;监护人：&nbsp;证件号码：</u></em></strong></p><p class="ql-align-center"><br></p><p class="ql-align-center"><s>说明：</s>如果申请人为未成年人，则必须填写监护人信息，授权人即为监护人。 在您授权后，浙江省医保个人权益信息授权查询和使用平台 会允许 场景的 服务访问/查询/使用您的医保个人权益信息。 医保个人权益信息收集查询及使用授权条款 本人授权并同意 场景的 服务，基于为本人/本人的未成年子女提供更优质服务和产品的目的，可自行或通过所委托的第三方技术服务机构向浙江省医疗保障局访问/查询/使用本人/本人未成年子女的的医保个人权益信息，并使用医保个人权益信息为本人/本人的未成年子女提供 服务，医保个人权益信息包括但不限于就诊信息、处方病历、医嘱信息、结算信息等所有因医疗就诊产生的数据。 本人授权并同意浙江省医疗保障局可向 场景的 服务提供本人医保个人权益信息。 以上信息的授权期间：自授权之日起至本人在 场景的 服务完成日止。 为确保本人信息的安全，浙江省医疗保障局对上述信息负有保密义务，并采取各种措施保证信息安全。&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p><ol><li class="ql-align-center ql-indent-1"><span class="ql-size-large ql-font-serif" style="color: rgb(230, 0, 0); background-color: rgb(255, 255, 0);">授权人姓名：</span><span class="ql-size-large">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></li></ol><h1 class="ql-align-center"><br></h1><p class="ql-align-center"><span class="ql-size-large">授权时间：（为当前日期）</span></p>`,
          agrmtName: "<p>退役军人管理局场景授权协议</p>",
        },
        // {
        //   agrmtName: "协议1",
        //   agrmtDscy: "xxxx",
        //   status: 0,
        // },
        // {
        //   agrmtName: "协议2",
        //   agrmtDscy: "xxxx2",
        //   status: 0,
        // },
        // {
        //   agrmtName: "协议3",
        //   agrmtDscy: "xxxx3",
        //   status: 0,
        // },
        // {
        //   agrmtName: "协议4",
        //   agrmtDscy: `1、医保服务在浙江省内的，均可网上申请办理。
        //       2、网上申请采取实名制，申请人必须为医保服务本人及未成年人子女。
        //       3、本查询记录的信息为医保服务信息或医保账户信息。
        //       4、网上申请必须填写 1、医保服务在浙江省内的，均可网上申请办理。
        //       2、网上申请采取实名制，申请人必须为医保服务本人及未成年人子女。
        //       3、本查询记录的信息为医保服务信息或医保账户信息。
        //       4、网上申请必须填写 1、医保服务在浙江省内的，均可网上申请办理。
        //       2、网上申请采取实名制，申请人必须为医保服务本人及未成年人子女。
        //       3、本查询记录的信息为医保服务信息或医保账户信息。
        //       4、网上申请必须填写 1、医保服务在浙江省内的，均可网上申请办理。
        //       2、网上申请采取实名制，申请人必须为医保服务本人及未成年人子女。
        //       3、本查询记录的信息为医保服务信息或医保账户信息。
        //       4、网上申请必须填写 1、医保服务在浙江省内的，均可网上申请办理。
        //       4、网上申请必须填写 1、医保服务在浙江省内的，均可网上申请办理。
        //       2、网上申请采取实名制，申请人必须为医保服务本人及未成年人子女。
        //       3、本查询记录的信息为医保服务信息或医保账户信息。
        //       4、网上申请必须填写 1、医保服务在浙江省内的，均可网上申请办理。
        //       2、网上申请采取实名制，申请人必须为医保服务本人及未成年人子女。
        //       3、本查询记录的信息为医保服务信息或医保账户信息。
        //       4、网上申请必须填写 1、医保服务在浙江省内的，均可网上申请办理。`,
        //   status: 0,
        // },
      ],
    };
  },
  watch: {
    // active: {
    //   handler(val) {
    //     this.$nextTick(() => {
    //       if (this.active >= 0) {
    //         if (
    //           document.getElementsByClassName("agreement-content")[val]
    //             .clientHeight >
    //           document.getElementsByClassName("content")[val].clientHeight
    //         ) {
    //           this.tabs[val].status = 1;
    //         }
    //       }
    //     });
    //   },
    //   // immediate: true,
    // },
  },
  //   created() {
  //     if (this.ids) {
  //       const ids = this.ids.split(",");
  //       getAgreements({ agrmtIds: ids, logTraceID: randomString() }).then(res => {
  //         this.tabs = res.records;
  //         this.active = 0;
  //       });
  //     } else {
  //       this.tabs = this.agreements;
  //       this.active = 0;
  //     }
  //   },
  mounted() {
    let vConsole = new Vconsole();
  },

  methods: {
    handlescroll(e) {
      console.log(e, "滚动");
    },
    // 点击遮罩层
    clickOverlay() {
      this.$emit("agreeEmit", this.agreementName, false);
    },
    // 判断是否滚动底部
    handleScroll(e) {
      const { scrollTop, clientHeight, scrollHeight } = e.target;
      if (scrollTop + clientHeight >= scrollHeight) {
        this.$nextTick(() => {
          this.tabs[this.active].status = 1;
        });
      }
    },
    beforeChange(a) {
      if (this.showBtn) return false;
      this.active = a;
    },
    refuse() {
      this.$emit("agreeEmit", this.agreementName, false);
    },
    readAndAgree() {
      if (this.tabs.length > this.active + 1) {
        this.active++;
      } else {
        this.$emit("agreeEmit", this.agreementName, true);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/css/public.scss";
.agreement {
  .agreement-title {
    @include font_size_14_20($fontSize-14);
  }
  .agreement-content {
    margin-top: 0.18rem;
    padding: 0 0.17rem;
    height: 52vh;
    overflow-y: scroll;
    h3 {
      font-size: 0.16rem;
    }
    p {
      color: #2b333f;
      @include font_size_14_20($fontSize-14);
    }
    &::-webkit-scrollbar {
      width: 0;
    }
    .content {
      height: 100%;
      overflow-y: scroll;
    }
  }
  .bottom {
    padding: 0.08rem 0.16rem 0 0.16rem;
    box-sizing: border-box;
    position: absolute;
    bottom: 0;
    display: flex;
    width: 100%;
    height: 0.71rem;
    justify-content: space-between;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.28);
    .van-button {
      // width: auto;
      @include width_168_auto($width-168);
      min-width: 1.33rem;
      // width: 1.68rem;
      height: 0.48rem;
      border-radius: 0.24rem;
      font-weight: 500;
      @include font_size_18_24($fontSize-18);
    }
  }
}
</style>
