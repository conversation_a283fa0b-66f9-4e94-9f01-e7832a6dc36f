!
<template>
  <div class="main">
    <!-- header -->
    <div class="header">
      <div>
        {{ this.$route.query.beginTime }} - {{ this.$route.query.endTime }}
      </div>
      <!-- this.$route.query.mallCode2 -->
    </div>
    <!-- sec -->
    <div class="sec">
      <div class="sec-one">
        <div class="one-one">
          <div>场景名称</div>
        </div>
        <div class="one-two">
          <div>{{ this.sceneInfo.sceneName }}</div>
        </div>
      </div>
      <br />
      <!-- <div class="sec-two" @click="btnExamine">
        <div class="two-one"><span></span>&nbsp;&nbsp;查看场景服务协议</div>
        <div class="two-two"></div>
      </div> -->
      <!-- ==== -->
      <div class="sec-thr">
        <div class="thr-one">
          <div>服务名称</div>
          <div>调用时间</div>
        </div>
        <div class="thr-two">
          <div
            class="two-one"
            v-for="(item, index) in RecordDetail"
            :key="index"
          >
            <div>{{ index + 1 }}&nbsp;&nbsp;{{ item.serviceName }}</div>
            <div style="margin-right: 0.2rem">{{ item.crteTime }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- footer -->
    <div class="footer">
      <div class="footer-one">
        <van-button
          @click="relieve"
          :disabled="sceneInfo.status !== '0'"
          type="info"
          >解除该场景的授权</van-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import { Dialog, Notify } from "vant";
import common, { randomString, encrypt } from "../../util/index";
import { channelDetail, chanChannel, querySceneCallLog } from "../../api/index";
export default {
  data() {
    return {
      RecordDetail: [],
      sceneInfo: {},
    };
  },
  methods: {
    // 根据场景查服务调用记录
    getAuthoChannel() {
      const data = {
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
        startTime: `${this.$route.query.beginTime} 00:00:00`,
        endTime: `${this.$route.query.endTime} 23:59:59`,
        sceneId: this.sceneInfo.sceneId,
        logTraceID: randomString(),
      };
      querySceneCallLog(data).then(res => {
        this.RecordDetail = res.records;
      });
    },
    // 解除授权
    getChannService() {
      let serviceIds = [];
      for (const item of this.RecordDetail) {
        serviceIds.push(item.serviceId);
      }
      let params = {
        logTraceID: randomString(),
        userEvictScenes: [
          {
            sceneId: this.sceneInfo.sceneId,
            serviceIds: serviceIds,
          },
        ],
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
      };
      chanChannel(params).then(res => {
        if (res.status == 200) {
          Notify({ type: "success", message: "解绑成功" });
          this.$router.push({
            path: "/channel",
          });
        }
      });
    },
    // 场景记录明细
    // getauthoDetail() {
    //   let params = {
    //     userMark: "33333",
    //     startDate: this.$route.query.mallCode,
    //     endDate: this.$route.query.mallCode1,
    //     channelCode: this.$route.query.mallCode2,
    //   };
    //   channelDetail(params).then(res => {
    //     this.RecordDetail = JSON.parse(res.data.data);
    //     console.log(
    //       JSON.parse(res.data.data),
    //       "startDate:this.$route.query.mallCode,"
    //     );
    //   });
    // },
    // 点击查看
    btnExamine() {
      this.$router.push({
        path: "/agreement",
        query: {
          serviceId: this.$route.query.mallCode4,
        },
      });
    },
    // 点击删除
    relieve() {
      Dialog.confirm({
        title: "解除授权确认",
        className: "Diy",
        message: "是否确认解除?",
        confirmButtonText: "解除",
        confirmButtonColor: "#255BDA",
        width: "2.79rem",
      })
        .then(() => {
          this.getChannService();
        })
        .catch(() => {
          // Notify({ type: 'primary', message: '取消解绑成功' });
        });
    },
  },
  created() {
    this.createTime = new Date().getTime();
    this.sceneInfo = JSON.parse(decodeURIComponent(this.$route.query.info));
    // this.getauthoDetail();
    this.getAuthoChannel();
  },
  mounted() {
    this.mountedTime = new Date().getTime();
    common.changeTitle("医保个人授权");
    this.$buryPoint(this.mountedTime - this.createTime);
  },
};
</script>
<style lang="scss" scoped>
@import "@/assets/css/public.scss";
::v-deep.van-hairline--top {
  border-top: 0.01rem solid #ececec;
  button {
    :nth-child(1) {
      border-right: 0.01rem solid #ececec;
    }
  }
}
.main {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f5;
  .header {
    width: 100%;
    height: 1.57rem;
    background: url("../../assets/image/beijing1.png") no-repeat center;
    background-size: cover;
    background-size: 100% 100%;
    div {
      font-size: 0.14rem;
      color: #ffffff;
      text-align: center;
      letter-spacing: 1px;
      padding: 25px;
    }
  }
  .sec {
    .sec-one {
      width: 3.45rem;
      height: 1.25rem;
      margin: -100px auto;
      background: url("../../assets/image/juxing2.png") no-repeat center;
      background-size: cover;
      background-size: 100% 100%;
      .one-one {
        padding: 30px;
        display: flex;
        font-size: 0.12rem;
        color: #555555;
        justify-content: space-between;
        div {
          flex: 1;
          text-align: left;
        }
        :nth-child(2) {
          padding-left: 0.3rem;
        }
      }
      .one-two {
        padding: 0 30px;
        display: flex;
        color: #333333;
        font-size: 0.16rem;
        justify-content: space-between;
        div {
          line-height: 0.2rem;
          font-weight: 550;
          text-align: left;
        }
        :nth-of-type(1) {
          flex: 7;
        }
        :nth-child(2) {
          flex: 6;
          font-size: 0.25rem;
          padding-left: 0.2rem;
        }
      }
    }
    .sec-two {
      width: 3rem;
      height: 0.2rem;
      line-height: 0.2rem;
      margin: 12px auto;
      padding: 15px 20px;
      border-radius: 0.1rem;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .two-one {
        display: flex;
        align-items: center;
        @include font_size_14_20($fontSize-14);
        line-height: 0.2rem;
        color: #363a44;
        vertical-align: middle !important;
        span {
          display: inline-block;
          width: 0.15rem;
          height: 0.15rem;
          line-height: 0.15rem;
          vertical-align: middle !important;
          background: url("../../assets/image/xieyi-icon.png") no-repeat center;
          background-size: cover;
          background-size: 100% 100%;
        }
      }
      .two-two {
        vertical-align: middle;
        font-size: 0.3em;
        padding: 0.1em;
        width: 0.03rem;
        height: 0.03rem;
        vertical-align: middle !important;
        border-top: 0.015rem solid #999999;
        border-right: 0.015rem solid #999999;
        transform: rotate(45deg);
      }
    }
    .sec-thr {
      margin-top: 12px !important;
      border-radius: 0.1rem;
      width: 3.45rem;
      // height: 3.16rem;
      height: calc(100vh - 3.4rem);
      overflow-y: scroll;
      margin: 0 auto;
      background: #ffffff;
      &::-webkit-scrollbar {
        width: 0;
      }
      .thr-one {
        width: 100%;
        color: #363a44;
        @include font_size_14_20($fontSize-14);
        height: 0.44rem;
        display: flex;
        align-items: center;
        border-top-left-radius: 0.08rem;
        border-top-right-radius: 0.08rem;
        display: flex;
        background: #e9f3fe !important;
        justify-content: space-between;
        div {
          flex: 1;
          font-weight: 550;
          padding-left: 0.3rem;
        }
      }
      .thr-two::-webkit-scrollbar {
        width: 0 !important;
      }
      .thr-two {
        overflow-y: scroll;
        overflow-x: hidden;
        @include font_size_14_20($fontSize-14);
        // padding: 0.15rem;
        & > div {
          box-sizing: border-box;
          padding: 0 0.15rem;
        }
        .two-one {
          width: 100%;
          line-height: 0.41rem;
          display: flex;
          justify-content: space-between;
          border-bottom: 0.01rem solid #ececec;
        }
      }
    }
  }
  .footer {
    width: 100%;
    height: 0.71rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.28);
    position: fixed;
    bottom: 0;
    .footer-one {
      padding: 0.16rem;
      width: 90%;
      height: 0.48rem;
      margin: 0 auto;
      vertical-align: middle;
      position: relative;
      .van-button {
        position: absolute;
        width: 90%;
        border-radius: 0.24rem;
        height: 0.48rem;
        vertical-align: middle;
        line-height: 0.48rem;
        @include font_size_16_22($fontSize-16);
      }
    }
  }
}
</style>
