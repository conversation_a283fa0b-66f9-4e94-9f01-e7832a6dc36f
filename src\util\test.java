public static byte[] SM3HashMac(String text,String key){
    //1.填充0至key,或者hashkey,使其长度为sm3分组长度
    /** BLOCK_LENGTHSM3分组长度 ,64个字节,512位*/
    byte[] sm3_key;
    byte[] structured_key=new byte[BLOCK_LENGTH];
    byte[] IPAD=new byte[BLOCK_LENGTH];
    byte[] OPAD=new byte[BLOCK_LENGTH];
    if(StringUtil.base64Decode(key).length>BLOCK_LENGTH){
        sm3_key= sm3Hash(StringUtil.base64Decode(key));
        System.arraycopy(sm3_key,0,structured_key,0,sm3_key.length);
    }else {
        System.arraycopy(StringUtil.base64Decode(key),0,structured_key,0,StringUtil.base64Decode(key).length);
    }
    //2.让处理之后的key 与ipad (分组长度的0x36)做异或运算
    for(int i=0;i<BLOCK_LENGTH;i++){
        IPAD[i]=0x36;
        OPAD[i]=0x5c;
    }
    byte[] ipadkey=XOR(structured_key,IPAD);
    //3.将2的结果与text拼接
    int textLen=text.getBytes().length;
    byte[] t3=new byte[BLOCK_LENGTH+textLen];
    System.arraycopy(ipadkey,0,t3,0,ipadkey.length);
    System.arraycopy(text.getBytes(),0,t3,ipadkey.length,text.getBytes().length);
    //4.将3的结果sm3 哈希
    byte[] t4=sm3Hash(t3);
    //5.让处理之后的key 与opad(分组长度的0x5c)做异或运算
    byte[] opadkey=XOR(structured_key,OPAD);
    //6.4的结果拼接在5之后
    byte[] t6=new byte[BLOCK_LENGTH+t4.length];
    System.arraycopy(opadkey,0,t6,0,opadkey.length);
    System.arraycopy(t4,0,t6,opadkey.length,t4.length);
    //7.对6做hash
    return (sm3Hash(t6));
}