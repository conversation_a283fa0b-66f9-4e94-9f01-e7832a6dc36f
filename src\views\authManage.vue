<!--Author: 郭元扬
ProjectDescription: 个人医保服务授权管理（服务列表页）
CreateTime: 2022-07-22
UpdateTime: 2022-07-22-->
<template>
  <div class="auth-manage">
    <div class="service-type">
      <!-- @click="changeServiceType(1)" -->
      <p :style="serviceType === 1 ? { color: '#428FFC' } : {}">个人服务</p>
      <!-- <p
        @click="changeServiceType(2)"
        :style="serviceType === 2 ? { color: '#428FFC' } : {}"
      >
        对公服务
      </p> -->
    </div>
    <div v-if="serviceType === 1 || (serviceType === 2 && business)">
      <p class="title">服务名称</p>
      <div v-if="serviceList.length">
        <ul>
          <li
            class="serve-list"
            v-for="item of serviceList"
            v-show="item.status === '1'"
            :key="item.serviceId"
          >
            <p>
              <span>{{ item.serviceName }}</span>
              <span style="color: #686b73">未授权</span>
            </p>
          </li>
          <li
            class="serve-list"
            v-for="(item, index) of serviceList"
            v-show="item.status === '0'"
            :key="index"
          >
            <p>
              <span>{{ item.serviceName }}</span>
              <span @click="handleService(item)"
                >已授权<van-icon color="#686b73" name="arrow"
              /></span>
            </p>
          </li>
        </ul>
      </div>
      <div v-if="noService" style="margin-top: 1.84rem">
        <empty />
      </div>
    </div>
    <div class="select-business" v-else>
      <p class="bg"></p>
      <div class="select">
        <img @click="showBusiness = true" src="@/assets/image/add.png" alt="" />
        <p>选择企业</p>
      </div>
    </div>
    <van-popup
      v-model="showBusiness"
      position="bottom"
      :style="{ height: '50%' }"
    >
      <van-picker
        title=""
        show-toolbar
        :columns="businessList"
        @confirm="onConfirm"
        @cancel="showBusiness = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { getAuthList } from "@/api";
import common from "@/util/index";
import { randomString, encrypt } from "@/util";
import empty from "@/components/empty.vue";
export default {
  components: {
    empty,
  },
  data() {
    return {
      serviceList: [],
      serviceObj: {}, // 单服务对应多场景的数据
      noService: false,
      serviceType: 1,
      businessList: this.$store.state.app.business,
      business: "",
      showBusiness: false,
    };
  },
  methods: {
    onConfirm(val) {
      this.showBusiness = false;
      this.business = val.value;
      this.getServiceList();
    },
    changeServiceType(type) {
      this.serviceType = type;
      if (type === 1) {
        this.getServiceList();
      }
    },
    // 已授权操作按钮
    handleService(info) {
      this.$router.push({
        path: "/authManageScene",
        query: {
          id: info.serviceId,
          name: info.serviceName,
          scenes: this.serviceObj[info.serviceId],
          business: this.serviceType === 2 ? this.business : "",
        },
      });
    },
    // 获取服务记录
    getServiceList() {
      let data = {
        logTraceID: randomString(),
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
      };
      if (this.serviceType === 1) {
        data.certNo = encrypt(this.$store.state.app.userInfo.idnum);
      } else {
        data.certNo = encrypt(this.business);
      }
      console.log(data, "data!!!!!!!!!!!!!!");
      getAuthList(data).then(res => {
        var obj = {};
        var arr = [];
        res.records.reduce((pre, cur) => {
          if (!obj[cur.serviceId]) {
            obj[cur.serviceId] = [cur];
            arr.push(cur);
          } else {
            obj[cur.serviceId].push(cur);
            if (cur.status === "0") {
              for (const item of arr) {
                if (item.serviceId === cur.serviceId) {
                  item.status = cur.status;
                }
              }
            }
          }
        }, []);
        if (res.records && res.records.length) {
          this.serviceList = arr;
          this.serviceObj = obj;
        } else {
          this.noService = true;
        }
      });
    },
  },
  created() {
    this.createTime = new Date().getTime();
    this.getServiceList();
    this.$buryPoint(this.mountedTime - this.createTime);
  },
  mounted() {
    this.mountedTime = new Date().getTime();
    common.changeTitle("医保个人授权");
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/css/public.scss";
.auth-manage {
  .title {
    width: 100%;
    line-height: 0.44rem;
    background: #f3f7f9;
    @include font_size_14_20($fontSize-14);
    color: #686b73;
    padding-left: 0.16rem;
    box-sizing: border-box;
  }
  .serve-list {
    p {
      line-height: 56px;
      margin-left: 0.17rem;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px #e8e9ec solid;
      span {
        &:nth-of-type(1) {
          @include font_size_16_22($fontSize-16);
          color: #363a44;
        }
        &:nth-of-type(2) {
          padding-right: 0.16rem;
          @include font_size_14_20($fontSize-14);
        }
      }
    }
  }
  .service-type {
    display: flex;
    height: 0.43rem;
    align-items: center;
    p {
      width: 100%;
      text-align: center;
      @include font_size_16_18($fontSize-16);
    }
  }
  .select-business {
    height: 100vh;
    background: #f6f7f8;
    .bg {
      height: 0.38rem;
    }
    .select {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: 1.7rem;
      background: white;
      img {
        width: 0.42rem;
        height: 0.45rem;
      }
      p {
        margin-top: 0.06rem;
        @include font_size_14_20($fontSize-14);
      }
    }
  }
}
</style>
