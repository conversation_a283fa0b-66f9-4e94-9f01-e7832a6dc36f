<template>
    <div class="home"></div>
</template>

<script>
import { getUserInfo, getUserInfoForWechat, getUserInfoByToken, test } from "@/api/index";
import { randomString } from "@/util/index";
import common from "@/util/index";
import serve from "@/components/inquire/serve.vue";
import accredit from "@/views/accredit.vue";
// import Vconsole from "vconsole";
export default {
    name: "Home",
    components: {
        serve,
        accredit,
    },
    data() {
        return {};
    },
    mounted() {
        // var vc = new Vconsole();
        // console.log(vc);
        
        // 开发模式快捷访问
        if (process.env.NODE_ENV === 'development') {
            const urlParams = new URLSearchParams(window.location.search);
            const directPage = urlParams.get('page');
            
            if (directPage === 'serve') {
                // 模拟用户信息
                this.$store.commit("app/setUserInfo", {
                    username: "测试用户",
                    idnum: "330724199502054815",
                    userid: "test_user_001",
                    idType: "ID_CARD"
                });
                this.$router.push('/serve');
                return;
            } else if (directPage === 'accredit') {
                // 模拟用户信息
                this.$store.commit("app/setUserInfo", {
                    username: "测试用户",
                    idnum: "330724199502054815",
                    userid: "test_user_001",
                    idType: "ID_CARD"
                });
                // 模拟sourceData
                window.sessionStorage.setItem("sourceData", JSON.stringify({
                    se: "scene_001",
                    sv: "service_001"
                }));
                this.$router.push('/accredit');
                return;
            }
        }
        
        this.login();
    },
    methods: {
        locationFresh() {
            // 更新跳转地址为新的独立部署环境
            const currentOrigin = window.location.origin;
            const redirectUrl = `https://puser.zjzwfw.gov.cn/sso/mobile.do?action=oauth&scope=1&servicecode=BCDSGA_8886a94ce5a4569b7d5ef5f38e7bc9cb&redirectUrl=${encodeURIComponent(currentOrigin + "/index.html")}`;
            window.location.replace(redirectUrl);
        },
        
        async login() {
            // 新的登录逻辑：从URL中获取token，通过第三方接口获取用户信息
            var url = decodeURIComponent(window.location.search);
            var object = {}; // 获取参数对象
            
            if (url.indexOf("?") != -1) {
                //url中存在问号，也就说有参数。
                var str = url.substring(url.indexOf("?") + 1); //得到?后面的字符串
                var strs = str.split("&"); //将得到的参数分隔成数组[id="123456",Name="bicycle"];
                for (var i = 0; i < strs.length; i++) {
                    // 如果key为callbackUrl,那么后面的链接上的参数都为这个key对应的value不需要分割
                    if (strs[i].split("=")[0] === "callbackUrl") {
                        object[strs[i].split("=")[0]] = url.substring(
                            url.indexOf("callbackUrl") + 12
                        );
                    } else {
                        object[strs[i].split("=")[0]] = strs[i].substring(
                            strs[i].indexOf("=") + 1
                        );
                    }
                }
            }
            
            // 保存业务数据
            this.sourceData = object.sourceData;
            if (object.sourceData) {
                window.sessionStorage.setItem("sourceData", object.sourceData);
            }
            if (object.callbackUrl) {
                window.sessionStorage.setItem(
                    "callbackUrl",
                    object.callbackUrl
                );
            }
            
            // 新的用户信息获取方式：从URL中获取token
            const token = common.getTokenFromUrl();
            console.log("从URL中获取到的token:", token);
            
            if (token) {
                // 使用token获取用户信息
                this.getUserInfoWithToken(token);
            // } else if (object.ticketId) {
            //     // 兼容原有的ticket方式（如果还需要的话）
            //     this.getUserInfoWithTicket(object.ticketId);
            } else {
                console.log("未找到token或ticket，跳转到登录页面");
                // this.locationFresh();
            }
        },
        
        // 使用token获取用户信息的新方法
        getUserInfoWithToken(token) {
            getUserInfoByToken({
                logTraceID: randomString(),
                token: token,
            })
                .then((res) => {
                    if (res.status === "200") {
                        const data = JSON.parse(res.data);
                        // 根据实际返回的数据结构调整字段映射
                        const userInfo = {
                            username: data.userName || data.username,
                            idnum: data.idNo || data.idnum,
                            userid: data.userId || data.userid,
                            idType: data.idType || "ID_CARD", // 默认身份证
                            ...data
                        };
                        this.$store.commit("app/setUserInfo", userInfo);
                        
                        // 根据业务数据决定跳转页面
                        if (window.sessionStorage.getItem("sourceData")) {
                            this.$router.push("/accredit");
                        } else {
                            this.$router.push("/serve");
                        }
                    } else {
                        console.error("获取用户信息失败:", res);
                        // this.locationFresh();
                    }
                })
                .catch((err) => {
                    console.error("获取用户信息异常:", err);
                    // this.locationFresh();
                });
        },
        
        // 兼容原有的ticket方式获取用户信息
        // getUserInfoWithTicket(ticket) {
        //     getUserInfoForWechat({
        //         logTraceID: randomString(),
        //         ticket: ticket,
        //     })
        //         .then((res) => {
        //             if (res.status === "200") {
        //                 const data = JSON.parse(res.data).personInfo;
        //                 data.username = data.userName;
        //                 data.idnum = data.idNo;
        //                 data.userid = data.userId;
        //                 this.$store.commit("app/setUserInfo", data);
        //                 if (window.sessionStorage.getItem("sourceData")) {
        //                     this.$router.push("/accredit");
        //                 } else {
        //                     this.$router.push("/serve");
        //                 }
        //             } else {
        //                 this.locationFresh();
        //             }
        //         })
        //         .catch((err) => {
        //             console.error("使用ticket获取用户信息失败:", err);
        //             this.locationFresh();
        //         });
        // }
    },
};
</script>
